/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.lambda

import com.jaguarlandrover.clc.exception.ClcOwnerVehicleAssociationEventsNotificationException
import io.quarkus.test.common.QuarkusTestResource
import software.amazon.awssdk.http.HttpStatusCode
import com.jaguarlandrover.clc.model.RoomServiceNotification
import com.jaguarlandrover.clc.service.JsonService
import com.jaguarlandrover.clc.service.ClcEventBusService
import com.jaguarlandrover.vroom.v2.dto.notification.EventType
import com.jaguarlandrover.vroom.v2.dto.vpAssociation.UserType
import jakarta.validation.Validation
import spock.lang.Shared
import spock.lang.Unroll
import spock.lang.Specification
import com.fasterxml.jackson.databind.ObjectMapper

@QuarkusTestResource
class ClcOwnerVehicleAssociationEventsNotificationHandlerSpec extends Specification {

    private JsonService jsonService
    private ClcEventBusService clcEventBusService
    private ClcOwnerVehicleAssociationEventsHandler clcBindingNotifEventHandler
    private ObjectMapper objectMapper
    public RoomServiceNotification roomServiceEvent

    def setup() {
        clcEventBusService = Mock()
        objectMapper = new ObjectMapper()
        jsonService = new JsonService()
        jsonService.validator = Validation.buildDefaultValidatorFactory().getValidator()
        roomServiceEvent = new RoomServiceNotification()
        clcBindingNotifEventHandler = new ClcOwnerVehicleAssociationEventsHandler(clcEventBusService, roomServiceEvent, jsonService)
    }

    @Shared private String userBoundEncodedString = encodeString(createDataMap("USER", "VIN", EventType.ASSOCIATION_CREATED, UserType.OWNER_KEY, "VPA"))
    @Shared private String svtBoundEncodedString = encodeString(createDataMap("USER", "VIN", EventType.ASSOCIATION_CREATED, UserType.SVT, "VPA"))
    @Shared private String userUnboundEncodedString = encodeString(createDataMap("USER", "VIN", EventType.ASSOCIATION_EXPIRED, UserType.OWNER_KEY, "VPA"))
    @Shared private String svtUnboundEncodedString = encodeString(createDataMap("USER", "VIN", EventType.ASSOCIATION_EXPIRED, UserType.SVT, 'VPA'))
    @Shared private String offboardUnboundEncodedString = encodeString(createDataMap("USER", "VIN", EventType.ASSOCIATION_EXPIRED, UserType.OFFBOARD, "VPA"))

    @Shared private Map<String, Object> svtBoundEventRequest = createHandlerEvent("LIVE.telematics.room-notifications.2", svtBoundEncodedString, "aws/kafka")
    @Shared private Map<String, Object> svtUnboundEventRequest = createHandlerEvent("LIVE.telematics.room-notifications.2", svtUnboundEncodedString, "aws/kafka")
    @Shared private Map<String, Object> offboardUnboundEventRequest = createHandlerEvent("LIVE.telematics.room-notifications.2", offboardUnboundEncodedString, "aws/kafka")
    @Shared private Map<String, Object> userBoundEventRequest = createHandlerEvent("LIVE.telematics.room-notifications.2", userBoundEncodedString, "aws/kafka")
    @Shared private Map<String, Object> userUnboundEventRequest = createHandlerEvent("LIVE.telematics.room-notifications.2", userUnboundEncodedString, "aws/kafka")

    @Unroll
    def"handleRequest successfully puts clc binding event to bus : handlerEvent"() {
        given: "A mock of kafka topic notification"
        List<Map<String, Object>> eventsList = []
        eventsList.add(handlerEvent)

        and: "A mock of putEvent"
        String eventId = UUID.randomUUID()
        clcEventBusService.putEvent(_, _, _) >> eventId

        when: "handleRequest is invoked"
        Map<String,Object> response = clcBindingNotifEventHandler.handleRequest(eventsList, null)

        then:
        response.statusCode == statusCode
        interaction * clcEventBusService.putEvent(
                "ecosystem-event-service",
                detailType,
                _) >> eventId

        where:
        handlerEvent                | detailType               | statusCode         | interaction
        svtBoundEventRequest        | "svt-bound-event"        | HttpStatusCode.OK  | 1
        svtUnboundEventRequest      | "svt-unbound-event"      | HttpStatusCode.OK  | 1
        offboardUnboundEventRequest | "offboard-unbound-event" | HttpStatusCode.OK  | 1
        userBoundEventRequest       | "user-bound-event"       | HttpStatusCode.OK  | 1
        userUnboundEventRequest     | "user-unbound-event"     | HttpStatusCode.OK  | 1
    }

    @Unroll
    def"handleRequest returns expected error message given RoomServiceNotification.Event with missing fields: handlerEvent"() {
        given: 'A mock of kafka topic notification'
        List<Map<String, String>> eventsList = []
        eventsList.add(handlerEvent)

        and: "an exception is thrown"
        roomServiceEvent.fromBase64ToEventDto(handlerEvent) >> { throw new Exception() }

        when: "handleRequest is invoked"
        clcBindingNotifEventHandler.handleRequest(eventsList, null)

        then:
        def exception = thrown(ClcOwnerVehicleAssociationEventsNotificationException)

        and: "error response contains expected message"
        exception.getMessage().contains(statusCode.toString())

        where:
        handlerEvent                                                                             | statusCode                           
        createHandlerEvent("", userBoundEncodedString, "aws/kafka")                              | HttpStatusCode.BAD_REQUEST           
        createHandlerEvent(null, userBoundEncodedString, "aws/kafka")                            | HttpStatusCode.BAD_REQUEST           
        createHandlerEvent("LIVE.telematics.room-notifications.2", userBoundEncodedString, "")   | HttpStatusCode.BAD_REQUEST           
        createHandlerEvent("LIVE.telematics.room-notifications.2", userBoundEncodedString, null) | HttpStatusCode.BAD_REQUEST           
        createHandlerEvent("LIVE.telematics.room-notifications.2", "", "aws/kafka")              | HttpStatusCode.BAD_REQUEST           
        createHandlerEvent("LIVE.telematics.room-notifications.2", null, "aws/kafka")            | HttpStatusCode.INTERNAL_SERVER_ERROR 
        null                                                                                     | HttpStatusCode.INTERNAL_SERVER_ERROR
    }

    @Unroll
    def"handleRequest returns expected response given RoomServiceNotification.Data with missing fields: userId, vin, eventType, userType, vpaId"() {
        given: "A mock of value in kafka topic message"
        String encodedString = encodeString(createDataMap(userId, vin, eventType, userType, vpaId))

        Map<String, String> events = new HashMap<>()
        events.put("topic", "LIVE.telematics.room-notifications.1")
        events.put("value", encodedString)
        events.put("eventSource", "aws/kafka")
        List<Map<String, String>> eventsList = List.of(events)

        and: "an exception is thrown"
        roomServiceEvent.fromBase64ToEventDto(events) >> { throw new Exception() }

        when: "handleRequest method is called"
        clcBindingNotifEventHandler.handleRequest(eventsList, null)

        then:
        def exception = thrown(ClcOwnerVehicleAssociationEventsNotificationException)

        and: "error response contains expected message"
        exception.getMessage().contains(statusCode.toString())

        where:
        userId | vin   | eventType                      | userType            | vpaId | statusCode
        ""     | "VIN" | EventType.ASSOCIATION_EXPIRED  | UserType.OWNER_KEY  | "VPA" | HttpStatusCode.BAD_REQUEST
        null   | "VIN" | EventType.ASSOCIATION_EXPIRED  | UserType.OWNER_KEY  | "VPA" | HttpStatusCode.BAD_REQUEST
        "USER" | ""    | EventType.ASSOCIATION_EXPIRED  | UserType.OWNER_KEY  | "VPA" | HttpStatusCode.BAD_REQUEST
        "USER" | null  | EventType.ASSOCIATION_EXPIRED  | UserType.OWNER_KEY  | "VPA" | HttpStatusCode.BAD_REQUEST
        "USER" | "VIN" | null                           | UserType.OWNER_KEY  | "VPA" | HttpStatusCode.BAD_REQUEST
        "USER" | "VIN" | EventType.ASSOCIATION_CREATED  | null                | "VPA" | HttpStatusCode.BAD_REQUEST
        "USER" | "VIN" | EventType.ASSOCIATION_CREATED  | UserType.FRIEND_KEY | ""    | HttpStatusCode.BAD_REQUEST
        "USER" | "VIN" | EventType.ASSOCIATION_CREATED  | UserType.FRIEND_KEY | null  | HttpStatusCode.BAD_REQUEST
    }
    
    Map<String, Object> createDataMap(String userId, String vin, EventType eventType, UserType userType, String vpaId) {
        Map<String, Object> eventMap = new HashMap<>();
        eventMap.put("userId", userId)
        eventMap.put("vin", vin)
        eventMap.put("eventType", eventType)
        eventMap.put("associationType", userType)
        eventMap.put("vpaId", vpaId)
        eventMap.put("userIdType", "FORGEROCK")
        eventMap.put("provisioned", true)
        eventMap.put("eventTimestamp", "2024-04-05T09:05:46.726+00:00")
        return eventMap
    }

    String encodeString(Map<String, Object> data) {
        ObjectMapper objectMapper = new ObjectMapper()
        String dataString = objectMapper.writeValueAsString(data)
        return Base64.getEncoder().encodeToString(dataString.getBytes())
    }

    Map<String, String> createHandlerEvent(String topic, String value, String eventSource) {
        int[] correlationIdList = [57, 97, 99, 102, 55, 51, 52, 49, 45, 52, 100, 102, 48, 45, 52, 53, 49, 52, 45, 98, 53, 99, 98, 45, 50, 99, 51, 53, 55, 51, 56, 97, 99, 48, 48, 49]
        Map<String, Object> events = new HashMap<>()
        events.put("topic", topic)
        events.put("value", value)
        events.put("eventSource", eventSource)
        events.put("headers", List.of(Map.of("correlationId", correlationIdList)))
        return events
    }
}
