/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.service

import com.jaguarlandrover.clc.exception.ClcBusPutEventException
import software.amazon.awssdk.services.eventbridge.EventBridgeClient
import software.amazon.awssdk.services.eventbridge.model.PutEventsRequest
import software.amazon.awssdk.services.eventbridge.model.PutEventsRequestEntry
import software.amazon.awssdk.services.eventbridge.model.PutEventsResponse
import software.amazon.awssdk.services.eventbridge.model.PutEventsResultEntry

import spock.lang.Specification

public class ClcEventBusServiceSpec extends Specification {

    private EventBridgeClient eventbridgeClient
    private ClcEventBusService clcEventBusService
    private String eventDetail
    private String detailType
    private String source
    private PutEventsRequest putEventsRequest

    def setup() {
        eventbridgeClient = Mock()
        clcEventBusService = new ClcEventBusService(eventbridgeClient)

        eventDetail = "{}"
        detailType = "test"
        source = "test"

        PutEventsRequestEntry entry = PutEventsRequestEntry.builder()
                .eventBusName("clc-event-bus")
                .source(source)
                .detail(eventDetail)
                .detailType(detailType)
                .build()

        putEventsRequest = PutEventsRequest.builder()
                .entries(entry)
                .build()
    }

    def "putEvent method is successfull"() {
        given: "Eventbridge put events response with event id"
        PutEventsResponse putEventsResponse = PutEventsResponse.builder()
            .entries(PutEventsResultEntry.builder()
                .eventId('00000')
                .build())
            .failedEntryCount(0)
            .build()

        when: "The putEvent method is called"
        String result = clcEventBusService.putEvent(source, detailType, eventDetail)

        then: "ClcPutEventResult is sent"
        1 * eventbridgeClient.putEvents(putEventsRequest) >> putEventsResponse
        assert result == "00000"
    }

    def "putEvent method throws error "() {
        given: "Eventbridge put events response with entry"
        PutEventsResponse putEventsResponse = PutEventsResponse.builder()
            .entries(PutEventsResultEntry.builder()
                .errorCode("InternalFailure")
                .errorMessage("failed")
                .build())
            .failedEntryCount(1)
            .build()

        when: "The putEvent method is called"
        clcEventBusService.putEvent(source, detailType, eventDetail)

        then: "ClcBusPutEventException is thrown"
        1 * eventbridgeClient.putEvents(putEventsRequest) >> putEventsResponse
        thrown ClcBusPutEventException
    }

}
