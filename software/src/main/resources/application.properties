quarkus.lambda.handler=${HANDLER}
clc.eventbus.name=clc-event-bus

# log levels definitions for specific packages
quarkus.log.console.format=%d{yyyy-MM-dd HH:mm:ss} %X{AWSRequestId} %-5p %c{1}:%L - %m%n
quarkus.log.category."root".level=INFO
quarkus.log.category."software.amazon.awssdk".level=WARN
quarkus.log.category."software.amazon.awssdk.request".level=WARN
%dev.quarkus.log.category."software.amazon.awssdk.request".level=DEBUG
%cn-dev.quarkus.log.category."software.amazon.awssdk.request".level=DEBUG

# Config property for the native image tool to build images avoiding architecture-specific optimizations not compatible with all systems
quarkus.native.additional-build-args=-H:ReflectionConfigurationFiles=reflection-config.json\,-H:DynamicProxyConfigurationFiles=proxy-config.json\,-H:ResourceConfigurationFiles=resources-config.json\,-march=compatibility
