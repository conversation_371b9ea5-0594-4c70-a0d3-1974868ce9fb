/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.lambda;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.jaguarlandrover.clc.exception.ClcInvalidRoomEventException;
import com.jaguarlandrover.clc.exception.ClcOwnerVehicleAssociationEventsNotificationException;
import com.jaguarlandrover.clc.exception.ClcValidationException;
import com.jaguarlandrover.clc.model.ClcOwnerVehicleAssociationEventsNotification;
import com.jaguarlandrover.clc.model.RoomServiceNotification;
import com.jaguarlandrover.clc.service.ClcEventBusService;
import com.jaguarlandrover.clc.service.JsonService;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.http.HttpStatusCode;

/**
 * Handler that decodes incoming notifications from Kafka via EventBridge Pipes
 * Validates the VPNotificationDto contained in the Notification and sends a new event to clc-event-bus in Ecosystem Integration
 */
@Slf4j
@Named("ClcOwnerVehicleAssociationEventsHandler")
public class ClcOwnerVehicleAssociationEventsHandler
        implements RequestHandler<List<Map<String, Object>>, Map<String, Object>> {
    private final RoomServiceNotification roomServiceNotification;
    private final JsonService jsonService;
    private final ClcEventBusService clcEventBusService;

    @Inject
    public ClcOwnerVehicleAssociationEventsHandler(ClcEventBusService clcEventBusService,
                                                   RoomServiceNotification roomServiceNotification, JsonService jsonService) {
        this.clcEventBusService = clcEventBusService;
        this.roomServiceNotification = roomServiceNotification;
        this.jsonService = jsonService;
    }

    /**
     * Handles incoming events from Kafka - EventBridge Pipes
     * The onFailure events go to DLQ
     *
     * @param events The Kafka notification from VROOM forwarded by EventBridge Pipes
     * @param context The handler context
     *
     * @return A map object sent as the detail in the event to clc-event-bus
     */
    public Map<String, Object> handleRequest(List<Map<String, Object>> events, Context context) {
        List<String> eventIdList = new ArrayList<>();
        try {
            List<RoomServiceNotification.Event> validatedEvents = events.stream()
                    .map(roomServiceNotification::fromBase64ToEventDto).toList();

            validatedEvents.forEach(event -> processEvent(event, eventIdList));

            return createResponse(HttpStatusCode.OK, "Room Service Notification event sent to VCDP.");

        } catch (ClcValidationException e) {
            log.error("Validation on field error: {}", e.getMessage(), e);
            String errorMessage = jsonService.toString(createErrorMessage(HttpStatusCode.BAD_REQUEST, e.getMessage()));
            throw new ClcOwnerVehicleAssociationEventsNotificationException(errorMessage);
        } catch (Exception e) {
            log.error("Service error: {}", e.getMessage(), e);
            String errorMessage = jsonService
                    .toString(createErrorMessage(HttpStatusCode.INTERNAL_SERVER_ERROR, e.getMessage()));
            throw new ClcOwnerVehicleAssociationEventsNotificationException(errorMessage);
        }
    }

    private void processEvent(RoomServiceNotification.Event event, List<String> eventIdList) {
        try {
            ClcOwnerVehicleAssociationEventsNotification mappedEvent =
                    ClcOwnerVehicleAssociationEventsNotification.fromRoomServiceNotificationEvent(event);
            String detailJson = jsonService.toString(mappedEvent.detail());
            String eventId = this.clcEventBusService.putEvent(mappedEvent.source(), mappedEvent.detailType(), detailJson);
            eventIdList.add(eventId);
        } catch (ClcInvalidRoomEventException e) {
            log.info(e.getMessage());
        }
    }

    /**
     * Creates an Error Response for onFailure events
     *
     * @param statusCode The response status code
     * @param message The string containing the error message
     * @return The message error JSON
     */
    private Map<String, Object> createErrorMessage(Integer statusCode, String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("statusCode", statusCode);
        response.put("errorMessage", message);
        return response;
    }

    /**
     * Creates a Response for successful events
     *
     * @param statusCode The response status code
     * @param message The string containing a message
     * @return The body JSON
     */
    private Map<String, Object> createResponse(Integer statusCode, String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("statusCode", statusCode);
        response.put("body", message);
        return response;
    }
}
