/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.exception;

import jakarta.validation.ConstraintViolation;
import java.util.Set;

/**
 * Custom exception used for errors related to dto validation
 *
 */
public class ClcValidationException extends RuntimeException {

    public ClcValidationException(String message) {

        super("Room binding notif event request validation failed: " + message);
    }

    public <T> ClcValidationException(Set<ConstraintViolation<T>> violations) {
        super(violations.stream()
                .findFirst()
                .map(ConstraintViolation::getMessage)
                .orElse(""));
    }
}
