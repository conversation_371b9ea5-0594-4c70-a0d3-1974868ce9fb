/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.configuration;

import jakarta.enterprise.inject.Produces;
import jakarta.inject.Singleton;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.eventbridge.EventBridgeClient;

/**
 * Configuration that allows injection of EventBridgeClient singleton dependency
 *
 */
public class EventBusConfiguration {
    Region region = Region.of(System.getenv().getOrDefault("AWS_REGION", "eu-west-1"));

    /**
     * It provides EventBridgeClient where needed in the application
     *
     * @return EventBridgeClient singleton dependency
     */
    @Produces
    @Singleton
    EventBridgeClient eventBridgeClient() {
        return EventBridgeClient.builder()
                .region(region)
                .build();
    }
}
