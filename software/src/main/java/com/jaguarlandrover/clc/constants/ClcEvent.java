/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.constants;

import java.util.HashMap;
import java.util.Map;
import com.jaguarlandrover.vroom.v2.dto.notification.EventType;
import com.jaguarlandrover.vroom.v2.dto.vpAssociation.UserType;
import io.quarkus.runtime.annotations.RegisterForReflection;


/**
 * Enum that represents the event detail-type in the Bound/Unbound flows
 *
 */
@RegisterForReflection
public enum ClcEvent {
    USER_BOUND("user-bound-event"),
    USER_UNBOUND("user-unbound-event"),
    OFFBOARD_UNBOUND("offboard-unbound-event"),
    SVT_BOUND("svt-bound-event"),
    SVT_UNBOUND("svt-unbound-event");

    private static final Map<EventType, Map<UserType, ClcEvent>> BY_ROOM_EVENT_AND_USER = new HashMap<>();

    static {
        BY_ROOM_EVENT_AND_USER.put(EventType.ASSOCIATION_CREATED, Map.of(
                UserType.FRIEND_KEY, USER_BOUND,
                UserType.OWNER_KEY, USER_BOUND,
                UserType.SVT, SVT_BOUND));

        BY_ROOM_EVENT_AND_USER.put(EventType.ASSOCIATION_EXPIRED, Map.of(
                UserType.FRIEND_KEY, USER_UNBOUND,
                UserType.OWNER_KEY, USER_UNBOUND,
                UserType.SVT, SVT_UNBOUND,
                UserType.OFFBOARD, OFFBOARD_UNBOUND));
    }

    private final String detailType;
    private final String source = "ecosystem-event-service";

    public String getSource() {
        return source;
    }

    public String getDetailType() {
        return detailType;
    }

    private ClcEvent(String detailType) {
        this.detailType = detailType;
    }


    /**
     * Maps to a detail-type by passing the VROOM's EventType and UserType
     *
     * @param roomEvent The EventType from VROOM
     * @param roomAssociation The UserType from VROOM
     *
     * @return ClcEvent enum representing the event detail-type
     */
    public static ClcEvent getByRoomEventAndUser(EventType roomEvent, UserType roomAssociation) {
        return BY_ROOM_EVENT_AND_USER.get(roomEvent).get(roomAssociation);
    }

}
