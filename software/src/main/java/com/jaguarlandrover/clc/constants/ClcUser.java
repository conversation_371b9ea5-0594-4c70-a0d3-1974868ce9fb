/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.constants;

import java.util.Map;
import com.jaguarlandrover.vroom.v2.dto.vpAssociation.UserType;
import io.quarkus.runtime.annotations.RegisterForReflection;


/**
 * Enum that represents the User Type coming from OneApp service
 *
 */
@RegisterForReflection
public enum ClcUser {
    PAAKOWNER,
    PAAKFRIEND;

    private static final Map<UserType, ClcUser> BY_ROOM_USER = Map.of(
            UserType.FRIEND_KEY, PAAKFRIEND,
            UserType.OWNER_KEY, PAAKOWNER,
            // Only owner have SVT association
            UserType.SVT, PAAKOWNER,
            /*
             * Both owner and friend can have offboard association. Currently, room does not
             * provide required info to identify user type, temporarily set to default
             * value.
             */
            UserType.OFFBOARD, PAAKOWNER);

    /**
     * Maps to a OneApp User Type by passing the VROOM User Type
     *
     * @param associationType The UserType from VROOM
     *
     * @return ClcUser enum representing the User Type coming from OneApp
     */
    public static ClcUser getByAssociation(UserType associationType) {
        return BY_ROOM_USER.get(associationType);
    }

}
