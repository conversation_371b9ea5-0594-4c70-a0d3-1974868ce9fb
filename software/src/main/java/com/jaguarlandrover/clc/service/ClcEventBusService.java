/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.service;

import com.jaguarlandrover.clc.exception.ClcBusPutEventException;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.eventbridge.EventBridgeClient;
import software.amazon.awssdk.services.eventbridge.model.PutEventsRequest;
import software.amazon.awssdk.services.eventbridge.model.PutEventsRequestEntry;
import software.amazon.awssdk.services.eventbridge.model.PutEventsResponse;
import software.amazon.awssdk.services.eventbridge.model.PutEventsResultEntry;

/**
 * Service responsible for forwarding events to clc-event-bus EventBus
 *
 */
@ApplicationScoped
@Slf4j
public class ClcEventBusService {
    private final EventBridgeClient eventBridgeClient;
    private final String clcBusName;

    public ClcEventBusService(EventBridgeClient eventBridgeClient) {
        this.eventBridgeClient = eventBridgeClient;
        this.clcBusName = System.getenv().getOrDefault("CLC_EVENT_BUS_NAME", "clc-event-bus");
    }

    /**
     * Sends an event to clc-event-bus EventBus
     *
     * @param source The source service name String
     * @param detailType The type of event in the bound/unbound flow
     * @param detail The string representing the event
     *
     * @return the event-id String
     */
    public String putEvent(String source, String detailType, String detail) {
        PutEventsRequestEntry entry = PutEventsRequestEntry.builder()
                .eventBusName(clcBusName)
                .source(source)
                .detail(detail)
                .detailType(detailType)
                .build();

        PutEventsRequest eventsRequest = PutEventsRequest.builder()
                .entries(entry)
                .build();

        try {
            log.info("Eventbridge Put Events Request: {}", eventsRequest);
            PutEventsResponse response = eventBridgeClient.putEvents(eventsRequest);
            log.info("Eventbridge Put Events Response: {}", response);
            PutEventsResultEntry entryResponse = response.entries().get(0);

            if (response.failedEntryCount() > 0) {
                throw new ClcBusPutEventException(entryResponse.toString(), entryResponse.errorCode());
            }

            return entryResponse.eventId();
        } catch (Exception e) {
            log.error("Service error: {}", e.getMessage(), e);
            throw new ClcBusPutEventException(e.getMessage(), e);
        }
    }
}
