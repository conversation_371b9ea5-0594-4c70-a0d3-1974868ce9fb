/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jaguarlandrover.clc.exception.ClcValidationException;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.validation.Validator;
import java.util.Map;
import lombok.AllArgsConstructor;

/**
 * Service responsible for serializing and deserializing JSON objects
 *
 */
@ApplicationScoped
@AllArgsConstructor
public class JsonService {
    private final ObjectMapper objectMapper;

    @Inject
    public Validator validator;

    public JsonService() {
        this.objectMapper = new ObjectMapper();
    }

    /**
     * Serializes a dto to a String
     *
     * @param object The dto to be serialized
     *
     * @return The String dto
     */
    public <T> String toString(T object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new ClcValidationException(e.getMessage());
        }
    }

    /**
     * Deserializes the String into a dto
     *
     * @param jsonString The String to be deserialized
     * @param objectClass the dto class name to be deserialized into
     *
     * @return The dto
     */
    public <T> T generateAs(String jsonString, Class<T> objectClass) {
        try {
            T request = objectMapper.readValue(jsonString, objectClass);
            return request;
        } catch (JsonProcessingException e) {
            throw new ClcValidationException(e.getMessage());
        }
    }

    /**
     * Converts a map object into a dto
     *
     * @param object The map object to be converted
     * @param objectClass the dto class name to be converted into
     *
     * @return The dto
     */
    public <T> T generateAs(Map<String, Object> object, Class<T> objectClass) {
        try {
            T request = objectMapper.convertValue(object, objectClass);
            return request;
        } catch (NullPointerException e) {
            throw new ClcValidationException(e.getMessage());
        }
    }
}
