/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.model;

import com.jaguarlandrover.clc.constants.ClcEvent;
import com.jaguarlandrover.clc.constants.ClcUser;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.jaguarlandrover.clc.exception.ClcInvalidRoomEventException;
import lombok.Builder;
import com.jaguarlandrover.vroom.v2.dto.notification.EventType;
import com.jaguarlandrover.vroom.v2.dto.person.SourceIdType;
import com.jaguarlandrover.vroom.v2.dto.vpAssociation.UserType;
import io.quarkus.runtime.annotations.RegisterForReflection;

/**
 * The dto representing the EventBus event to be sent to clc-event-bus in Ecosystem Integration
 *
 */
@Builder
@RegisterForReflection
public record ClcOwnerVehicleAssociationEventsNotification(
        String source,
        String detailType,
        Detail detail) {

    @Builder
    @RegisterForReflection
    public record Detail(
            Data data,
            Metadata metadata) {
    }

    @Builder
    @RegisterForReflection
    public record Data(
            String userId,
            String vin,
            String vpaId,
            ClcUser userType,
            SourceIdType userIdType) {
    }

    @Builder
    @RegisterForReflection
    public record Metadata(
            String correlationId,
            Integer schemaVersion) {
    }


    /**
     * Builds a ClcOwnerVehicleAssociationEventsNotification model from the decoded VROOM Kafka Notification
     *
     * @param event The decoded Kafka notification from VROOM forwarded by EventBridge Pipes
     *
     * @return ClcOwnerVehicleAssociationEventsNotification representing the event to be sent to clc-event-bus
     */
    public static ClcOwnerVehicleAssociationEventsNotification fromRoomServiceNotificationEvent(
            RoomServiceNotification.Event event) {
        EventType  roomEvent = event.data().getEventType();
        UserType association = event.data().getAssociationType();

        ClcEvent clcEvent = ClcEvent.getByRoomEventAndUser(roomEvent, association);
        ClcUser clcUser = ClcUser.getByAssociation(association);

        Data data = Data.builder()
                .userId(event.data().getUserId())
                .vin(event.data().getVin())
                .vpaId(event.data().getVpaId())
                .userType(clcUser)
                .userIdType(event.data().getUserIdType())
                .build();

        Metadata metadata = Metadata.builder()
                .correlationId(fromAsciiToString(event.headers()))
                .schemaVersion(1)
                .build();

        Detail detail = Detail.builder()
                .metadata(metadata)
                .data(data)
                .build();

        if (clcEvent != null) {
            return ClcOwnerVehicleAssociationEventsNotification.builder()
                    .source(clcEvent.getSource())
                    .detailType(clcEvent.getDetailType())
                    .detail(detail)
                    .build();
        }
        throw new ClcInvalidRoomEventException(event.data().getAssociationType().toString());
    }

    /**
     * Converts the Kafka Notification headers from byte array to a String
     *
     * @param headers The byte array representing the Kafka Notification headers
     *
     * @return Kafka Notification headers as String containing the correlation-id
     */
    public static String fromAsciiToString(List<Map<String, Object>> headers) {

        String defaultCorrelationId = "00000000-0000-0000-0000-000000000000";
        List<Integer> asciiList = (List<Integer>) headers.stream()
                .filter(header -> header.containsKey("correlationId"))
                .findFirst().orElse(Map.of("correlationId", defaultCorrelationId)).get("correlationId");

        // Convert the array of ASCII values to a string
        return asciiList.stream()
            .map(i -> (char) i.intValue())
            .map(String::valueOf)
            .collect(Collectors.joining());
    }
}
