/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.jaguarlandrover.clc.exception.ClcValidationException;
import com.jaguarlandrover.clc.service.JsonService;
import com.jaguarlandrover.vroom.v2.dto.notification.VpNotificationDto;
import io.quarkus.runtime.annotations.RegisterForReflection;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import jakarta.validation.constraints.NotBlank;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.Builder;

/**
 * The dto representing the VROOM Kafka Notification encoded as Base64
 * It also builds a VpNotificationDto dto that is readable after conversion
 *
 */
@ApplicationScoped
@RegisterForReflection
public class RoomServiceNotification {

    private final JsonService jsonService;
    @Inject
    public static Validator validator;

    public RoomServiceNotification() {
        this.jsonService =  new JsonService();
    }

    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    @RegisterForReflection
    public record Event(
            @NotBlank(message = "Topic may not be blank") String topic,
            @NotBlank (message = "Value may not be blank") String value,
            @NotBlank (message = "Source may not be blank") String eventSource,
            List<Map<String, Object>> headers,
            VpNotificationDto  data
    ) { }


    /**
     * Builds an Event dto from the encoded VROOM Kafka Notification
     * It also validates the VpNotificationDto that is present in the Notification
     *
     * @param kafkaEvent The encoded Kafka notification from VROOM forwarded by EventBridge Pipes
     *
     * @return Event in a readable format representing the encoded event received from EventBridge Pipes
     */
    public Event fromBase64ToEventDto(Map<String, Object> kafkaEvent) {
        Event event = jsonService.generateAs(kafkaEvent, Event.class);
        String decodedValue = decodedString(event.value());
        VpNotificationDto data = jsonService.generateAs(decodedValue, VpNotificationDto.class);
        validate(event);
        validate(data);
        return Event.builder()
                .topic(event.topic())
                .value(decodedValue)
                .headers(event.headers())
                .eventSource(event.eventSource())
                .data(data)
                .build();
    }

    /**
     * Decodes the Base64 string value received in the Kafka Notification
     *
     * @param encodedString The encoded Kafka notification string
     *
     * @return String in a readable format
     */
    public String decodedString(String encodedString) {
        byte[] stringByte = Base64.getDecoder().decode(encodedString);
        return new String(stringByte);
    }

    /**
     * Java Bean validation
     *
     * @param object The dto to be validated
     *
     * @throws ClcValidationException on violation of the validation constraints
     */
    public <T> void validate(T object) {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
        Set<ConstraintViolation<T>> violations = validator.validate(object);
        if (!violations.isEmpty()) {
            throw new ClcValidationException(violations);
        }
    }
}
