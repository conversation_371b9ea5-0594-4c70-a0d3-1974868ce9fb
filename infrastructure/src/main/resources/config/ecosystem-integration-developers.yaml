#
# API Gateway Configurations
#
region: eu-west-1
deploymentStage: dev
quarkusProfile: dev
#
# AWS Accounts
#
account: ************ # nosemgrep: generic.secrets.security.detected-aws-account-id.detected-aws-account-id
#
# MSK VPC configuration
#
vpcName: ecosystem-integration-developers-vpc
vpcId: vpc-00912d39a2f9e8e4f
subnetIds:
  - subnet-0f8d582b8ef7514a6
  - subnet-020a18af8fa7278e8
  - subnet-05570de30d1bed4d5
availabilityZones:
  - eu-west-1c
  - eu-west-1a
  - eu-west-1b

awsPartition: aws
#
# Resource Arns
#
vcdpEventBusRulesArn: arn:aws:events:eu-west-2:************:rule/clc-event-bus/*
vcdpEventBusArn: arn:aws:events:eu-west-2:************:event-bus/clc-event-bus
roomServiceMskClusterArn: arn:aws:kafka:eu-west-1:************:cluster/msk-eco-dev/84a82fe9-bee2-4fad-95a4-d4b32f2a9a1d-1
awsResourcesArnPrefix: arn:aws:%s:eu-west-1:************
#
# Datadog configuration
#
datadogJavaAgentJvmParams: -XX:+TieredCompilation -XX:TieredStopAtLevel=1
datadogApiKeySecretFullArn: arn:aws:secretsmanager:eu-west-1:************:secret:DdApiKeySecret-VEUYCJ7uzPYs-bLiyMh
ddExtensionArn: arn:aws:lambda:eu-west-1:************:layer:Datadog-Extension:55
ddTraceJavaArn: arn:aws:lambda:eu-west-1:************:layer:dd-trace-java:13

#
# Tags
#
env: ecosystem-integration-developers
service: clc-binding-eventbus-eco
managedBy: cdk
piiDataHandler: true
productOwner: <EMAIL>
squad: smoked-paprika
repoUrl: https://git-gdd.sdo.jlrmotor.com/D9/telematics-applications/vehicle-lifecycle/customer-lifecycle/clc-binding-eventbus-eco