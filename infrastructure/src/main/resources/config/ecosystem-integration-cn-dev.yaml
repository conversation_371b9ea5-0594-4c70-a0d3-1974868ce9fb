#
# API Gateway Configurations
#
region: cn-northwest-1
#seperate deployment stage and quarkus profile for CN.
deploymentStage: dev
quarkusProfile: cn-dev
#
# AWS Accounts
#
account: ************ # nosemgrep: generic.secrets.security.detected-aws-account-id.detected-aws-account-id
#
# MSK VPC configuration
#
vpcName: ecosystem-integration-cn-dev
vpcId: vpc-0f5603b05b55f5861
subnetIds:
  - subnet-0f21fbd6b9b403791
  - subnet-0fa105de3d532ec6a
  - subnet-0695f1d97007644b2
availabilityZones:
  - cn-northwest-1a
  - cn-northwest-1b
  - cn-northwest-1c

awsPartition: aws-cn

#
# Resource Arns
#
vcdpEventBusRulesArn: arn:aws-cn:events:cn-northwest-1:************:rule/clc-event-bus/*
vcdpEventBusArn: arn:aws-cn:events:cn-northwest-1:************:event-bus/clc-event-bus
roomServiceMskClusterArn: arn:aws-cn:kafka:cn-northwest-1:************:cluster/msk-eco-cn-dev/792c9907-cf81-4e6d-9e88-c8e5ef392f6e-4
awsResourcesArnPrefix: arn:aws-cn:%s:cn-northwest-1:************

#
# Datadog configuration
#
datadogJavaAgentJvmParams: -XX:+TieredCompilation -XX:TieredStopAtLevel=1
datadogApiKeySecretFullArn: arn:aws-cn:secretsmanager:cn-northwest-1:************:secret:DdApiKeySecret-hMI2wA
ddExtensionArn: arn:aws-cn:lambda:cn-northwest-1:************:layer:datadog-lambda-extension:1
ddTraceJavaArn: arn:aws-cn:lambda:cn-northwest-1:************:layer:dd-trace-java:1

#
# Tags
#
env: ecosystem-integration-cn-dev
service: clc-binding-eventbus-eco
managedBy: cdk
piiDataHandler: true
productOwner: <EMAIL>
squad: smoked-paprika
repoUrl: https://git-gdd.sdo.jlrmotor.com/D9/telematics-applications/vehicle-lifecycle/customer-lifecycle/clc-binding-eventbus-eco