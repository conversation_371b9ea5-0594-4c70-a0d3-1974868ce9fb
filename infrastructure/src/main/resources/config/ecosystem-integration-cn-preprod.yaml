#
# API Gateway Configurations
#
region: cn-northwest-1
#seperate deployment stage and quarkus profile for CN.
deploymentStage: preprod
quarkusProfile: cn-preprod
#
# AWS Accounts
#
account: ***********1 # nosemgrep: generic.secrets.security.detected-aws-account-id.detected-aws-account-id
#
# MSK VPC configuration
#
vpcName: ecosystem-integration-cn-preprod
vpcId: vpc-052d5308ab0f1e8e1
subnetIds:
  - subnet-0b614e94e33c73fed
  - subnet-002f44388322b9e61
  - subnet-00177644ae0ab86af
availabilityZones:
  - cn-northwest-1a
  - cn-northwest-1b
  - cn-northwest-1c

awsPartition: aws-cn

#
# Resource Arns
#
vcdpEventBusRulesArn: arn:aws-cn:events:cn-northwest-1:************:rule/clc-event-bus/*
vcdpEventBusArn: arn:aws-cn:events:cn-northwest-1:************:event-bus/clc-event-bus
roomServiceMskClusterArn: arn:aws-cn:kafka:cn-northwest-1:***********1:cluster/msk-eco-cn-preprod/a3542521-e0c2-4567-898a-7ea863b92ca1-3
awsResourcesArnPrefix: arn:aws-cn:%s:cn-northwest-1:***********1

#
# Datadog configuration
#
datadogJavaAgentJvmParams: -XX:+TieredCompilation -XX:TieredStopAtLevel=1
datadogApiKeySecretFullArn: arn:aws-cn:secretsmanager:cn-northwest-1:***********1:secret:clc-eventbridge-vroom-eco-secret-IEiQcH
ddExtensionArn: arn:aws-cn:lambda:cn-northwest-1:***********1:layer:datadog-lambda-extension:1
ddTraceJavaArn: arn:aws-cn:lambda:cn-northwest-1:***********1:layer:dd-trace-java:1

#
# Tags
#
env: ecosystem-integration-cn-preprod
service: clc-binding-eventbus-eco
managedBy: cdk
piiDataHandler: true
productOwner: <EMAIL>
squad: smoked-paprika
repoUrl: https://git-gdd.sdo.jlrmotor.com/D9/telematics-applications/vehicle-lifecycle/customer-lifecycle/clc-binding-eventbus-eco