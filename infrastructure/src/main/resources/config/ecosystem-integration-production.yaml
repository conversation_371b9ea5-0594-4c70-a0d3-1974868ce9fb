#
# API Gateway Configurations
#
region: eu-west-1
deploymentStage: prod
quarkusProfile: prod
#
# AWS Accounts
#
account: ************ # nosemgrep: generic.secrets.security.detected-aws-account-id.detected-aws-account-id
#
# MSK VPC Configurations
#
vpcName: ecosystem-integration-production-vpc
vpcId: vpc-0b5781b2229d37eea
subnetIds:
  - subnet-0275c23de20b1014e
  - subnet-035971d4b1b809a72
  - subnet-0d56c0f99315ab8c9
availabilityZones:
  - eu-west-1c
  - eu-west-1a
  - eu-west-1b
awsPartition: aws

#
# Resource Arns
#
vcdpEventBusRulesArn: arn:aws:events:eu-west-2:************:rule/clc-event-bus/*
vcdpEventBusArn: arn:aws:events:eu-west-2:************:event-bus/clc-event-bus
roomServiceMskClusterArn: arn:aws:kafka:eu-west-1:************:cluster/msk-eco-prod/f30af22b-e4da-46e2-99b7-2ab62de3a1aa-9
awsResourcesArnPrefix: arn:aws:%s:eu-west-1:************
#
# Datadog configuration
#
datadogJavaAgentJvmParams: -XX:+TieredCompilation -XX:TieredStopAtLevel=1
datadogApiKeySecretFullArn: arn:aws:secretsmanager:eu-west-1:************:secret:DdApiKeySecret-yEtS9RS97Ck4-U2OGwG
ddExtensionArn: arn:aws:lambda:eu-west-1:************:layer:Datadog-Extension:55
ddTraceJavaArn: arn:aws:lambda:eu-west-1:************:layer:dd-trace-java:13

#
# Tags
#
env: ecosystem-integration-pre-production
service: clc-binding-eventbus-eco
managedBy: cdk
piiDataHandler: true
productOwner: <EMAIL>
squad: smoked-paprika
repoUrl: https://git-gdd.sdo.jlrmotor.com/D9/telematics-applications/vehicle-lifecycle/customer-lifecycle/clc-binding-eventbus-eco