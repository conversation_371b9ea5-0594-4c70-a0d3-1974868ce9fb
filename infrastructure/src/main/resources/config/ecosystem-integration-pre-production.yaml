#
# API Gateway Configurations
#
region: eu-west-1
deploymentStage: preprod
quarkusProfile: pre-prod
#
# AWS Accounts
#
account: ************ # nosemgrep: generic.secrets.security.detected-aws-account-id.detected-aws-account-id
#
# MSK VPC Configurations
#
vpcName: ecosystem-integration-pre-production-vpc
vpcId: vpc-0b4c09bde490bab68
subnetIds:
  - subnet-0a30d2f17bc21e688
  - subnet-0646d98db629f10d1
  - subnet-007ac8eb079d4b6a1
availabilityZones:
  - eu-west-1c
  - eu-west-1a
  - eu-west-1b
awsPartition: aws

#
# Resource Arns
#
vcdpEventBusRulesArn: arn:aws:events:eu-west-2:************:rule/clc-event-bus/*
vcdpEventBusArn: arn:aws:events:eu-west-2:************:event-bus/clc-event-bus
roomServiceMskClusterArn: arn:aws:kafka:eu-west-1:************:cluster/msk-eco-preprod/9053dbe4-ba6d-4ec0-82e8-5ae525a57c66-6
awsResourcesArnPrefix: arn:aws:%s:eu-west-1:************
#
# Datadog configuration
#
datadogJavaAgentJvmParams: -XX:+TieredCompilation -XX:TieredStopAtLevel=1
datadogApiKeySecretFullArn: arn:aws:secretsmanager:eu-west-1:************:secret:DdApiKeySecret-kJkYIa0iCEUV-wbJSs7
ddExtensionArn: arn:aws:lambda:eu-west-1:************:layer:Datadog-Extension:55
ddTraceJavaArn: arn:aws:lambda:eu-west-1:************:layer:dd-trace-java:13

#
# Tags
#
env: ecosystem-integration-pre-production
service: clc-binding-eventbus-eco
managedBy: cdk
piiDataHandler: true
productOwner: <EMAIL>
squad: smoked-paprika
repoUrl: https://git-gdd.sdo.jlrmotor.com/D9/telematics-applications/vehicle-lifecycle/customer-lifecycle/clc-binding-eventbus-eco