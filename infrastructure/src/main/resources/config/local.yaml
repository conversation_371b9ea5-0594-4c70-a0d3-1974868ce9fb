#
# API Gateway Configurations
#
region: eu-west-1
deploymentStage: local
#
# AWS Accounts
#
account: ************ # nosemgrep: generic.secrets.security.detected-aws-account-id.detected-aws-account-id
#
# Resource Arns
#
vcdpEventBusRulesArn: arn:aws:events:eu-west-2:************:rule/clc-event-bus/*
#
# Tags
#
env: ecosystem-integration-developers
service: clc-binding-eventbus-eco
managedBy: cdk
piiDataHandler: true
productOwner: <EMAIL>
squad: smoked-paprika
repoUrl: https://git-gdd.sdo.jlrmotor.com/D9/telematics-applications/vehicle-lifecycle/customer-lifecycle/clc-binding-eventbus-eco