/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.util;

import com.jaguarlandrover.clc.config.Config;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

public class ResourceTags {

    private static final String DD_VERSION_FORMAT = "yyyy-MM-dd-Hm";

    private ResourceTags() {
        // HIDE CONSTRUCTOR
    }

    /**
     * Generate version number for Version Tag.
     *
     * @return build version number for Version Tag
     */
    private static String generateVersion() {
        return new SimpleDateFormat(DD_VERSION_FORMAT).format(new Date());
    }

    public static Map<String, String> getTags(Config config) {
        return Map.of(
                "env", config.getEnv(),
                "service", config.getService(),
                "version", generateVersion(),
                "managed_by", config.getManagedBy(),
                "pii_data_handler", config.getPiiDataH<PERSON><PERSON>(),
                "product_owner", config.getProductOwner(),
                "squad", config.getSquad(),
                "repo_url", config.getRepoUrl());
    }
}
