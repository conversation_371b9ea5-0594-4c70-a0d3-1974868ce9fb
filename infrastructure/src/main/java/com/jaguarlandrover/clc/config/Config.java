/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.config;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Config {
    private String account;
    private String region;
    private String deploymentStage;
    private String quarkusProfile;
    private String vcdpEventBusRulesArn;
    private String vcdpEventBusArn;
    private String roomServiceMskClusterArn;
    private String awsResourcesArnPrefix;

    // tag values
    private String env;
    private String service;
    private String managedBy;
    private String piiDataHandler;
    private String productOwner;
    private String squad;
    private String repoUrl;

    // VPC values
    private String vpcName;
    private String vpcId;
    private List<String> subnetIds;
    private List<String> availabilityZones;
    private String awsPartition;

    // Datadog values
    private String datadogJavaAgentJvmParams;
    private String datadogApiKeySecretFullArn;
    private String ddTraceJavaArn;
    private String ddExtensionArn;
}

