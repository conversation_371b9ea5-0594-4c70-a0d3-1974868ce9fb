/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.File;
import java.io.IOException;

public class ConfigLoader {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigLoader.class);
    private static final String DEFAULT_CONFIG_ENV = "local";

    private ConfigLoader() {
        // HIDE CONSTRUCTOR
    }

    public static Config loadConfiguration(String env) {
        if (env == null || env.isEmpty()) {
            env = DEFAULT_CONFIG_ENV;
        }
        LOGGER.info("configEnv: {}", env);
        File file = new File(Thread.currentThread().getContextClassLoader()
                .getResource(String.format("config/%s.yaml", env)).getFile());

        ObjectMapper om = new ObjectMapper(new YAMLFactory());

        try {
            LOGGER.info("Configuration file read.");
            return om.readValue(file, Config.class);
        } catch (IOException e) {
            LOGGER.error(e.getMessage(), e);
            return new Config();
        }
    }
}
