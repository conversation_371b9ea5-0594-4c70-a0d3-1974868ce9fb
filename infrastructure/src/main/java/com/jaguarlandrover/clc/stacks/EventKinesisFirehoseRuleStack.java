/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.stacks;

import com.jaguarlandrover.clc.config.Config;
import java.util.List;
import software.amazon.awscdk.RemovalPolicy;
import software.amazon.awscdk.Stack;
import software.amazon.awscdk.StackProps;
import software.amazon.awscdk.services.events.EventBus;
import software.amazon.awscdk.services.events.EventPattern;
import software.amazon.awscdk.services.events.Match;
import software.amazon.awscdk.services.events.Rule;
import software.amazon.awscdk.services.events.targets.KinesisStream;
import software.amazon.awscdk.services.iam.IRole;
import software.amazon.awscdk.services.iam.Role;
import software.amazon.awscdk.services.kinesis.Stream;
import software.amazon.awscdk.services.kinesisfirehose.CfnDeliveryStream.KinesisStreamSourceConfigurationProperty;
import software.amazon.awscdk.services.kinesisfirehose.CfnDeliveryStream.BufferingHintsProperty;
import software.amazon.awscdk.services.kinesisfirehose.CfnDeliveryStream.ExtendedS3DestinationConfigurationProperty;
import software.amazon.awscdk.services.kinesisfirehose.CfnDeliveryStream;
import software.amazon.awscdk.services.logs.LogGroup;
import software.amazon.awscdk.services.logs.LogStream;
import software.amazon.awscdk.services.s3.Bucket;
import software.amazon.awscdk.services.s3.IBucket;
import software.constructs.Construct;

public class EventKinesisFirehoseRuleStack extends Stack {

    public List<String> ruleArnList;

    public EventKinesisFirehoseRuleStack(final Construct parent, final String id, final StackProps props,
                                         EventBus bus, Config config) {
        super(parent, id, props);

        final String region =  config.getRegion();
        final String deploymentEnv = config.getDeploymentStage();

        IRole kinesisFirehoseRole = Role.fromRoleName(this, "jlr-gdd-eventbridge-kinesis-firehose-role", "jlr-gdd-eventbridge-kinesis-firehose-role");

        String auditBucketName =
                "eventbus-audit-" + config.getAccount() + "-" + region + "-" + deploymentEnv;
        IBucket destinationBucket = Bucket.fromBucketName(this, auditBucketName, auditBucketName);

        Rule toKinesisRule = Rule.Builder.create(this, "clc-events-to-kinesis-rule")
                .ruleName("clc-events-to-kinesis-rule")
                .eventBus(bus)
                .eventPattern(EventPattern.builder()
                        .source(Match.exists())
                        .detailType(Match
                                .exists())
                        .build())
                .build();

        Stream stream = Stream.Builder.create(this, "clc-event-bus-stream")
                .streamName("clc-event-bus-stream")
                .build();

        toKinesisRule.addTarget(KinesisStream.Builder.create(stream)
                .build());

        LogGroup deliveryStreamLogGroup = LogGroup.Builder.create(this, "clc-event-bus-delivery-stream-logs")
                .logGroupName("clc-delivery-stream-logs")
                .removalPolicy(RemovalPolicy.DESTROY)
                .build();

        LogStream logStream = LogStream.Builder.create(this, "MyLogStream")
                .logGroup(deliveryStreamLogGroup)
                .logStreamName("clc-eventbus-audit")
                .removalPolicy(RemovalPolicy.DESTROY)
                .build();

        ExtendedS3DestinationConfigurationProperty s3ConfigurationProperty = ExtendedS3DestinationConfigurationProperty
                .builder()
                .roleArn(kinesisFirehoseRole.getRoleArn())
                .bucketArn(destinationBucket.getBucketArn())
                .prefix(bus.getEventBusName() + "/")
                .bufferingHints(
                        BufferingHintsProperty.builder()
                                .intervalInSeconds(60)
                                .build())
                .cloudWatchLoggingOptions(CfnDeliveryStream.CloudWatchLoggingOptionsProperty.builder()
                        .enabled(true)
                        .logGroupName(deliveryStreamLogGroup.getLogGroupName())
                        .logStreamName(logStream.getLogStreamName())
                        .build())
                .build();

        CfnDeliveryStream.Builder.create(this, "clc-event-bus-delivery-stream")
                .deliveryStreamName("clc-event-bus-delivery-stream")
                .deliveryStreamType("KinesisStreamAsSource")
                .kinesisStreamSourceConfiguration(
                        KinesisStreamSourceConfigurationProperty.builder()
                                .kinesisStreamArn(stream.getStreamArn())
                                .roleArn(kinesisFirehoseRole.getRoleArn())
                                .build())
                .extendedS3DestinationConfiguration(s3ConfigurationProperty)
                .build();

        this.ruleArnList = List.of(toKinesisRule.getRuleArn());
    }
}