/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.stacks;

import com.jaguarlandrover.clc.config.Config;
import com.google.gson.Gson;
import com.jaguarlandrover.clc.util.ResourceTags;
import software.amazon.awscdk.App;
import software.amazon.awscdk.CustomResource;
import software.amazon.awscdk.Stack;
import software.amazon.awscdk.StackProps;
import software.amazon.awscdk.services.lambda.Function;
import software.amazon.awscdk.services.lambda.IFunction;
import software.constructs.Construct;
import java.util.List;
import java.util.Map;

public class EventBridgeTagsStack extends Stack {

    public EventBridgeTagsStack(final App parent, final String id) {
        this(parent, id, null, null, null);
    }

    public EventBridgeTagsStack(final Construct parent, final String id, final StackProps props,
                                final List<String> resourceArns, Config config) {
        super(parent, id, props);

        Gson gson = new Gson();

        IFunction function = Function.fromFunctionName(this, "elc-shared-tag-event-rules-function",
                "elc-shared-tag-event-rules-function");

        CustomResource.Builder.create(this, "clc-tag-event-bridge-rules")
                .serviceToken(function.getFunctionArn())
                .properties(Map.of(
                        "ResourceArns", String.join(",", resourceArns),
                        "Tags", gson.toJson(ResourceTags.getTags(config))))
                .build();
    }
}
