/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.stacks;

import software.amazon.awscdk.Stack;
import software.amazon.awscdk.StackProps;
import software.amazon.awscdk.services.events.EventBus;
import software.amazon.awscdk.services.eventschemas.CfnDiscoverer;
import software.constructs.Construct;

public class EventSchemaDiscoveryStack extends Stack {

    public EventSchemaDiscoveryStack(final Construct scope, final String id, EventBus eventBus) {
        this(scope, id, null, null);
    }

    public EventSchemaDiscoveryStack(final Construct scope, final String id, final StackProps props, EventBus eventBus) {
        super(scope, id, props);

        CfnDiscoverer.Builder.create(this, "clc-binding-event-bus-discoverer")
                .sourceArn(eventBus.getEventBusArn())
                .crossAccount(false)
                .description("Event Schema Discovery for Customer Lifecycle EventBus")
                .build();
    }
}