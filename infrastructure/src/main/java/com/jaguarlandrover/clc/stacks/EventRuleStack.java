/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.stacks;

import com.jaguarlandrover.clc.config.Config;
import java.util.List;
import com.jaguarlandrover.clc.constructs.OwnerVehicleEventsRoomNotificationRule;
import com.jaguarlandrover.clc.constructs.VcdpEventBusListenerRule;
import software.amazon.awscdk.App;
import software.amazon.awscdk.Stack;
import software.amazon.awscdk.StackProps;
import software.amazon.awscdk.services.events.EventBus;
import software.amazon.awscdk.services.lambda.Function;
import software.amazon.awscdk.services.lambda.IFunction;
import software.constructs.Construct;

public class EventRuleStack extends Stack {

    private static final String CLC_EVENT_BRIDGE_TO_VROOM_FUNC_NAME = "clc-event-bridge-to-vroom-lambda";
    public List<String> ruleArnList;

    public EventRuleStack(final App parent, final String id) {
        this(parent, id, null, null, null);
    }

    public EventRuleStack(final Construct parent, final String id, final StackProps props, EventBus eventBus, Config config) {
        super(parent, id, props);

        // Get clc event bridge to vroom function
        IFunction clcEventBridgeToVroomFunction = Function.fromFunctionName(this, this.CLC_EVENT_BRIDGE_TO_VROOM_FUNC_NAME,
                this.CLC_EVENT_BRIDGE_TO_VROOM_FUNC_NAME);

        VcdpEventBusListenerRule vcdpEventBusListenerRule = new VcdpEventBusListenerRule(this,
                "clc-vcdp-event-bus-listener-rule", eventBus, clcEventBridgeToVroomFunction);

        OwnerVehicleEventsRoomNotificationRule ownerVehicleEventsRoomNotificationRule =
                new OwnerVehicleEventsRoomNotificationRule(this,
                        "clc-owner-vehicle-association-events-room-notification-rule", config, eventBus);

        this.ruleArnList = List.of(
                vcdpEventBusListenerRule.ruleArn,
                ownerVehicleEventsRoomNotificationRule.getOwnerVehicleAssociationEventsRoomNotificationRuleArn());
    }
}
