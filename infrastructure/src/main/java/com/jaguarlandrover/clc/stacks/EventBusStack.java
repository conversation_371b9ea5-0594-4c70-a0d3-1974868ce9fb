/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.stacks;

import com.jaguarlandrover.clc.config.Config;
import com.jaguarlandrover.clc.constructs.EventBridgePipes;
import software.amazon.awscdk.App;
import software.amazon.awscdk.Stack;
import software.amazon.awscdk.StackProps;
import software.amazon.awscdk.services.events.EventBus;
import software.amazon.awscdk.services.iam.AnyPrincipal;
import software.amazon.awscdk.services.iam.Effect;
import software.amazon.awscdk.services.iam.PolicyStatement;
import software.constructs.Construct;
import java.util.List;
import java.util.Map;

public class EventBusStack extends Stack {

    public EventBus bus;

    public EventBusStack(final App parent, final String id) {
        this(parent, id, null, null);
    }

    public EventBusStack(final Construct parent, final String id, final StackProps props, Config config) {
        super(parent, id, props);

        this.bus = EventBus.Builder.create(this, "clc-event-bus")
                .eventBusName("clc-event-bus")
                .build();

        // Add Resource-based policy to receive events from vcdp eventbus
        this.bus.addToResourcePolicy(PolicyStatement.Builder.create()
                .sid("AllowEventFromVcdpClcEventBus")
                .effect(Effect.ALLOW)
                .principals(List.of(new AnyPrincipal()))
                .actions(List.of("events:PutEvents"))
                .resources(List.of(bus.getEventBusArn()))
                .conditions(Map.of("ArnLike", Map.of("aws:SourceArn", config.getVcdpEventBusRulesArn())))
                .build());

        new EventBridgePipes(this, "ClcEventBridgePipes", config, this.bus);
    }
}
