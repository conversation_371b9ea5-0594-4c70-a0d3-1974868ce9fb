/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.constructs;

import static com.jaguarlandrover.clc.constants.Constants.EVENT_RULE_TARGET_RETRY_COUNT;
import static com.jaguarlandrover.clc.constants.Constants.INCOMING_VCDP_EVENT_DETAIL_TYPES;
import static com.jaguarlandrover.clc.constants.Constants.TWO_HOURS_IN_SEC;

import software.amazon.awscdk.services.events.CfnRule;
import software.amazon.awscdk.services.events.EventBus;
import software.amazon.awscdk.services.iam.Effect;
import software.amazon.awscdk.services.iam.PolicyStatement;
import software.amazon.awscdk.services.iam.ServicePrincipal;
import software.amazon.awscdk.services.lambda.IFunction;
import software.amazon.awscdk.services.lambda.Permission;
import software.amazon.awscdk.services.sqs.Queue;
import software.constructs.Construct;
import java.util.List;
import java.util.Map;

public class VcdpEventBusListenerRule extends Construct {

    public final String ruleArn;

    public VcdpEventBusListenerRule(final Construct scope, final String id, EventBus eventBus,
                                    IFunction clcEventBridgeToVroomFunction) {
        super(scope, id);

        // Dead letter queue for events coming from vcdp event bus
        Queue vcdpEventBusEventsDlq = Queue.Builder.create(this, "clc-vcdp-event-bus-events-dlq")
                .queueName("clc-vcdp-event-bus-events-dlq")
                .build();

        // Vcdp event bus listener rule
        CfnRule vcdpEventBusListenerRule = CfnRule.Builder.create(this, "clc-vcdp-event-bus-listener-rule")
                .name("clc-vcdp-event-bus-listener-rule")
                .state("ENABLED")
                .eventBusName(eventBus.getEventBusName())
                .eventPattern(Map.of("source", List.of("vcdp-event-enrichment-service"),
                        "detail-type", INCOMING_VCDP_EVENT_DETAIL_TYPES))
                .targets(List.of(CfnRule.TargetProperty.builder()
                        .id("clc-event-bridge-to-vroom-lambda-target")
                        .arn(clcEventBridgeToVroomFunction.getFunctionArn())
                        .deadLetterConfig(CfnRule.DeadLetterConfigProperty.builder()
                                .arn(vcdpEventBusEventsDlq.getQueueArn())
                                .build())
                        .retryPolicy(CfnRule.RetryPolicyProperty.builder()
                                .maximumRetryAttempts(EVENT_RULE_TARGET_RETRY_COUNT)
                                .maximumEventAgeInSeconds(TWO_HOURS_IN_SEC)
                                .build())
                        .build()))
                .build();

        // Add permission to clc event bridge to vroom function to be invoked by vcdpEventBusListenerRule
        clcEventBridgeToVroomFunction
                .addPermission("clc-vcdp-event-bus-listener-rule-invoke-lambda-permission", Permission.builder()
                        .sourceArn(vcdpEventBusListenerRule.getAttrArn())
                        .principal(new ServicePrincipal("events.amazonaws.com"))
                        .action("lambda:InvokeFunction")
                        .build());

        // Add Resource-based policy to vcdpEventBusEventsDlq to receive messages from vcdpEventBusListenerRule
        vcdpEventBusEventsDlq.addToResourcePolicy(PolicyStatement.Builder.create()
                .sid("AllowMessagesFromVcdpEventBusListenerRule")
                .effect(Effect.ALLOW)
                .principals(List.of(new ServicePrincipal("events.amazonaws.com")))
                .actions(List.of("sqs:SendMessage"))
                .resources(List.of(vcdpEventBusEventsDlq.getQueueArn()))
                .conditions(Map.of("ArnLike", Map.of("aws:SourceArn", vcdpEventBusListenerRule.getAttrArn())))
                .build());

        ruleArn = vcdpEventBusListenerRule.getAttrArn();
    }
}
