/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.constructs;

import static com.jaguarlandrover.clc.constants.Constants.INCOMING_KAFKA_EVENT_DETAIL_TYPES;

import com.jaguarlandrover.clc.config.Config;
import java.util.List;
import lombok.Getter;
import software.amazon.awscdk.services.events.EventBus;
import software.amazon.awscdk.services.events.EventPattern;
import software.amazon.awscdk.services.events.Rule;
import software.amazon.awscdk.services.events.targets.EventBusProps;
import software.amazon.awscdk.services.sqs.Queue;
import software.constructs.Construct;

@Getter
public class OwnerVehicleEventsRoomNotificationRule extends Construct {

    private String ownerVehicleAssociationEventsRoomNotificationRuleArn;

    public OwnerVehicleEventsRoomNotificationRule(final Construct scope, final String id, Config config,
                                                  EventBus eventBus) {
        super(scope, id);

        // Owner Vehicle bound / unbound events notification from Vroom
        Rule eventServiceListenerRule = Rule.Builder.create(this, "clc-owner-vehicle-association-events-room-notification-rule")
                .ruleName("clc-owner-vehicle-association-events-room-notification-rule")
                .eventPattern(EventPattern.builder()
                        .source(List.of("ecosystem-event-service"))
                        .detailType(INCOMING_KAFKA_EVENT_DETAIL_TYPES)
                        .build())
                .eventBus(eventBus)
                .build();

        Queue roomServiceNotificationRuleDlq = Queue.Builder.create(this, "clc-owner-vehicle-association-events-room-notification-dlq")
                .queueName("clc-owner-vehicle-association-events-room-notification-dlq")
                .build();

        eventServiceListenerRule.addTarget(new software.amazon.awscdk.services.events.targets.EventBus(
                EventBus.fromEventBusArn(this, "clc-owner-vehicle-association-events-room-notification-rule-target",
                        config.getVcdpEventBusArn()),
                EventBusProps.builder()
                        .deadLetterQueue(roomServiceNotificationRuleDlq)
                        .build()));

        ownerVehicleAssociationEventsRoomNotificationRuleArn = eventServiceListenerRule.getRuleArn();
    }
}
