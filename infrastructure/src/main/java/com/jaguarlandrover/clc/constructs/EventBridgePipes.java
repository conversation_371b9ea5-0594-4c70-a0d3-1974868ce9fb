/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.constructs;

import static com.jaguarlandrover.clc.constants.Constants.CLC_EVENTBRIDGE_PIPES;
import static com.jaguarlandrover.clc.constants.Constants.CLOUDWATCH_LOGGROUP_PREFIX;
import static com.jaguarlandrover.clc.constants.Constants.ROOM_SERVICE_VP_NOTIFICATION_KAFKA_TOPIC;

import com.jaguarlandrover.clc.config.Config;
import java.util.List;
import software.amazon.awscdk.RemovalPolicy;
import software.amazon.awscdk.services.events.EventBus;
import software.amazon.awscdk.services.iam.Effect;
import software.amazon.awscdk.services.iam.PolicyStatement;
import software.amazon.awscdk.services.iam.Role;
import software.amazon.awscdk.services.iam.ServicePrincipal;
import software.amazon.awscdk.services.logs.LogGroup;
import software.amazon.awscdk.services.logs.RetentionDays;
import software.amazon.awscdk.services.pipes.CfnPipe;
import software.amazon.awscdk.services.pipes.CfnPipe.PipeTargetLambdaFunctionParametersProperty;
import software.amazon.awscdk.services.pipes.CfnPipe.PipeTargetParametersProperty;
import software.constructs.Construct;

public class EventBridgePipes extends Construct {

    public EventBridgePipes(final Construct scope, final String id, Config config, EventBus eventBus) {
        super(scope, id);

        OwnerVehicleEventsRoomNotificationLambda
                ownerVehicleEventsRoomNotificationLambda = new OwnerVehicleEventsRoomNotificationLambda(
                this, "clc-owner-vehicle-association-events-room-notification-lambda", config, eventBus);

        PolicyStatement kafkaConnectPolicyStatement = PolicyStatement.Builder.create()
                .sid("AllowStreamFromKafkaClusterSource")
                .effect(Effect.ALLOW)
                .actions(List.of(
                        "kafka:DescribeCluster",
                        "kafka:DescribeClusterV2",
                        "kafka:GetBootstrapBrokers",
                        "kafka-cluster:Connect",
                        "kafka-cluster:DescribeGroup",
                        "kafka-cluster:AlterGroup",
                        "kafka-cluster:DescribeTopic",
                        "kafka-cluster:ReadData",
                        "kafka-cluster:DescribeClusterDynamicConfiguration"
                ))
                .resources(List.of("*"))
                .build();

        PolicyStatement vpcResourcesAccessPolicyStatement = PolicyStatement.Builder.create()
                .sid("AllowAccessToVPCResources")
                .effect(Effect.ALLOW)
                .actions(List.of(
                        "ec2:DescribeNetworkInterfaces",
                        "ec2:DescribeSubnets",
                        "ec2:DescribeSecurityGroups",
                        "ec2:DescribeVpcs",
                        "ec2:CreateNetworkInterface",
                        "ec2:DeleteNetworkInterface"
                ))
                .resources(List.of("*"))
                .build();

        PolicyStatement enrichmentStepLambdaPolicyStatement = PolicyStatement.Builder.create()
                .sid("AllowAccessToLambdaResource")
                .effect(Effect.ALLOW)
                .actions(List.of("lambda:InvokeFunction"))
                .resources(List.of(ownerVehicleEventsRoomNotificationLambda.getOwnerVehicleAssociationEventsRoomNotificationLambdaArn()))
                .build();

        PolicyStatement cloudWatchLoggingPolicyStatement = PolicyStatement.Builder.create()
                .sid("AllowCloudWatchLogging")
                .effect(Effect.ALLOW)
                .actions(List.of(
                        "logs:CreateLogStream",
                        "logs:PutLogEvents"
                ))
                .resources(List.of("*"))
                .build();

        Role eventBridgePipesExecutionRole = Role.Builder.create(this, "clc-eventbridge-pipes-execution-role")
                .roleName("clc-eventbridge-pipes-execution-role")
                .assumedBy(new ServicePrincipal("pipes.amazonaws.com"))
                .build();

        eventBridgePipesExecutionRole.addToPolicy(kafkaConnectPolicyStatement);
        eventBridgePipesExecutionRole.addToPolicy(vpcResourcesAccessPolicyStatement);
        eventBridgePipesExecutionRole.addToPolicy(cloudWatchLoggingPolicyStatement);
        eventBridgePipesExecutionRole.addToPolicy(enrichmentStepLambdaPolicyStatement);

        LogGroup logGroup = LogGroup.Builder.create(this, CLC_EVENTBRIDGE_PIPES + "-logGroup")
                .logGroupName(CLOUDWATCH_LOGGROUP_PREFIX + CLC_EVENTBRIDGE_PIPES)
                .retention(RetentionDays.ONE_DAY)
                .removalPolicy(RemovalPolicy.DESTROY)
                .build();

        CfnPipe.Builder.create(this, CLC_EVENTBRIDGE_PIPES)
                .name(CLC_EVENTBRIDGE_PIPES)
                .roleArn(eventBridgePipesExecutionRole.getRoleArn())
                .source(config.getRoomServiceMskClusterArn())
                .sourceParameters(CfnPipe.PipeSourceParametersProperty.builder()
                        .managedStreamingKafkaParameters(
                                CfnPipe.PipeSourceManagedStreamingKafkaParametersProperty
                                        .builder()
                                        .startingPosition("LATEST")
                                        .batchSize(1)
                                        .topicName(ROOM_SERVICE_VP_NOTIFICATION_KAFKA_TOPIC)
                                        .build())
                        .build())
                .target(ownerVehicleEventsRoomNotificationLambda
                        .getOwnerVehicleAssociationEventsRoomNotificationLambdaArn())
                .targetParameters(PipeTargetParametersProperty.builder()
                        .lambdaFunctionParameters(PipeTargetLambdaFunctionParametersProperty.builder()
                                .invocationType("FIRE_AND_FORGET")
                                .build())
                        .build())
                .logConfiguration(CfnPipe.PipeLogConfigurationProperty.builder()
                        .cloudwatchLogsLogDestination(CfnPipe.CloudwatchLogsLogDestinationProperty.builder()
                                .logGroupArn(logGroup.getLogGroupArn())
                                .build())
                        .level("INFO")
                        .build())
                .build();
    }
}
