/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.constructs;

import static com.jaguarlandrover.clc.constants.Constants.EVENT_LAMBDA_DLQ_NAME;
import static com.jaguarlandrover.clc.constants.Constants.HANDLER_NAME;
import static com.jaguarlandrover.clc.constants.Constants.HANDLER_ROLE;
import static java.util.Map.entry;

import com.jaguarlandrover.clc.config.Config;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import software.amazon.awscdk.Duration;
import software.amazon.awscdk.RemovalPolicy;
import software.amazon.awscdk.services.ec2.IVpc;
import software.amazon.awscdk.services.ec2.SecurityGroup;
import software.amazon.awscdk.services.ec2.Vpc;
import software.amazon.awscdk.services.ec2.VpcAttributes;
import software.amazon.awscdk.services.events.EventBus;
import software.amazon.awscdk.services.iam.Effect;
import software.amazon.awscdk.services.iam.ManagedPolicy;
import software.amazon.awscdk.services.iam.PolicyStatement;
import software.amazon.awscdk.services.iam.Role;
import software.amazon.awscdk.services.iam.ServicePrincipal;
import software.amazon.awscdk.services.lambda.Code;
import software.amazon.awscdk.services.lambda.Function;
import software.amazon.awscdk.services.lambda.FunctionProps;
import software.amazon.awscdk.services.lambda.ILayerVersion;
import software.amazon.awscdk.services.lambda.LayerVersion;
import software.amazon.awscdk.services.lambda.Runtime;
import software.amazon.awscdk.services.lambda.Tracing;
import software.amazon.awscdk.services.logs.LogGroup;
import software.amazon.awscdk.services.logs.RetentionDays;
import software.amazon.awscdk.services.sqs.IQueue;
import software.amazon.awscdk.services.sqs.Queue;
import software.amazon.awscdk.services.sqs.QueueEncryption;
import software.amazon.awscdk.services.sqs.QueueProps;
import software.constructs.Construct;

@Getter
public class OwnerVehicleEventsRoomNotificationLambda extends Construct {

    private String ownerVehicleAssociationEventsRoomNotificationLambdaArn;

    public OwnerVehicleEventsRoomNotificationLambda(final Construct scope, final String id, Config config, EventBus eventBus) {
        super(scope, id);

        // VPC reference
        IVpc vpc = Vpc.fromVpcAttributes(this, config.getVpcName(), VpcAttributes.builder()
                .vpcId(config.getVpcId())
                .privateSubnetIds(config.getSubnetIds())
                .availabilityZones(config.getAvailabilityZones())
                .build());

        // Security group to allow all outbound traffic from lambda inside VPC
        SecurityGroup securityGroup = SecurityGroup.Builder.create(this, id + HANDLER_NAME)
                .securityGroupName(id + HANDLER_NAME)
                .allowAllOutbound(true)
                .description("Security group for outbound traffic from clc-eventbridge-pipe-enrichment-step-lambda")
                .vpc(vpc)
                .build();

        // Extensions for lambda function to forward traces to Datadog
        List<ILayerVersion> layersList = List.of(
                LayerVersion.fromLayerVersionArn(this, "DatadogExtensionLayerFromArn-" + id,
                        config.getDdExtensionArn()),
                LayerVersion.fromLayerVersionArn(this, "DatadogTraceJavaLayerFromArn-" + id,
                        config.getDdTraceJavaArn()));

        Role lambdaExecutionRole = createLambdaIamRole(config, eventBus.getEventBusName());

        IQueue deadLetterQueue = new Queue(this, EVENT_LAMBDA_DLQ_NAME, QueueProps.builder()
                .queueName(EVENT_LAMBDA_DLQ_NAME)
                .encryption(QueueEncryption.SQS_MANAGED)
                .build());

        deadLetterQueue.grantSendMessages(lambdaExecutionRole);

        FunctionProps functionProps = FunctionProps.builder()
                .runtime(Runtime.PROVIDED_AL2023)
                .code(Code.fromAsset("../software/target/function.zip"))
                .handler("io.quarkus.amazon.lambda.runtime.QuarkusStreamHandler::handleRequest")
                .functionName(HANDLER_NAME)
                .role(lambdaExecutionRole)
                .timeout(Duration.seconds(30L))
                .memorySize(1024)
                .tracing(Tracing.ACTIVE)
                .vpc(vpc)
                .securityGroups(List.of(securityGroup))
                .layers(layersList)
                .deadLetterQueue(deadLetterQueue)
                .deadLetterQueueEnabled(true)
                .logGroup(createLogGroup(HANDLER_NAME, "/aws/lambda/"))
                .environment(getEnvironmentVariables(HANDLER_NAME, config))
                .build();

        Function ownerVehicleAssociationEventsRoomNotificationLambda = new Function(this, HANDLER_NAME, functionProps);
        ownerVehicleAssociationEventsRoomNotificationLambdaArn = ownerVehicleAssociationEventsRoomNotificationLambda.getFunctionArn();
    }

    // Lambda environment variables
    private Map<String, String> getEnvironmentVariables(String handlerName, Config config) {
        // Env variables for handler and Datadog config
        return Map.ofEntries(
                entry("QUARKUS_PROFILE", config.getQuarkusProfile()),
                entry("DISABLE_SIGNAL_HANDLERS", "true"),
                entry("QUARKUS_LAMBDA_HANDLER", handlerName),
                entry("JAVA_TOOL_OPTIONS",
                        "-javaagent:\"/opt/java/lib/dd-java-agent.jar\" "
                                + config.getDatadogJavaAgentJvmParams()),
                entry("DD_LOGS_INJECTION", "true"),
                entry("DD_JMXFETCH_ENABLED", "false"),
                entry("DD_TRACE_ENABLED", "true"),
                entry("DD_MERGE_XRAY_TRACES", "true"),
                entry("DD_SITE", "datadoghq.eu"),
                entry("DD_SERVICE", config.getService()),
                entry("AWS_LAMBDA_EXEC_WRAPPER", "/opt/datadog_wrapper"),
                entry("DD_API_KEY_SECRET_ARN", config.getDatadogApiKeySecretFullArn()));
    }

    private LogGroup createLogGroup(String handlerName, String cloudWatchLogGroupPrefix) {
        return LogGroup.Builder.create(this, handlerName + "-logGroup")
                .logGroupName(cloudWatchLogGroupPrefix + handlerName)
                .retention(RetentionDays.ONE_DAY)
                .removalPolicy(RemovalPolicy.DESTROY)
                .build();
    }

    private Role createLambdaIamRole(Config config, String eventBusName) {
        // Grant permission to Lambda function to get the Datadog API Secrets
        PolicyStatement secretsPolicyStatement = PolicyStatement.Builder.create()
                .sid("AllowGetSecretValue")
                .effect(Effect.ALLOW)
                .actions(List.of("secretsmanager:GetSecretValue"))
                .resources(List.of(config.getDatadogApiKeySecretFullArn()))
                .build();

        //Allow put events in EventBus
        PolicyStatement eventsPolicyStatement = PolicyStatement.Builder.create()
                .sid("AllowToPutEvents")
                .effect(Effect.ALLOW)
                .actions(List.of("events:PutEvents"))
                .resources(List.of("arn:" + config.getAwsPartition() + ":events:" + config.getRegion() + ":"
                        + config.getAccount() + ":event-bus/" + eventBusName))
                .build();

        PolicyStatement kmsPolicyStatement = PolicyStatement.Builder.create()
                .sid("AllowActioningSQS")
                .effect(Effect.ALLOW)
                .actions(List.of(
                        "kms:GenerateDataKey",
                        "kms:Decrypt"))
                .resources(List.of(String.format(config.getAwsResourcesArnPrefix() + ":key/*", "kms")))
                .build();

        Role lambdaExecutionRole = Role.Builder.create(this, HANDLER_ROLE)
                .roleName(HANDLER_ROLE)
                .assumedBy(new ServicePrincipal("lambda.amazonaws.com"))
                .build();
        lambdaExecutionRole.addManagedPolicy(ManagedPolicy.fromAwsManagedPolicyName("service-role/AWSLambdaBasicExecutionRole"));
        lambdaExecutionRole.addManagedPolicy(ManagedPolicy.fromAwsManagedPolicyName("service-role/AWSLambdaVPCAccessExecutionRole"));
        lambdaExecutionRole.addToPolicy(secretsPolicyStatement);
        lambdaExecutionRole.addToPolicy(kmsPolicyStatement);
        lambdaExecutionRole.addToPolicy(eventsPolicyStatement);

        return lambdaExecutionRole;
    }
}
