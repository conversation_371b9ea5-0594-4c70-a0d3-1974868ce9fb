/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc.constants;

import java.util.List;

public class Constants {

    public static final int TWO_HOURS_IN_SEC = 2 * 60 * 60;
    public static final int EVENT_RULE_TARGET_RETRY_COUNT = 2;

    public static final List<String> INCOMING_VCDP_EVENT_DETAIL_TYPES = List.of(
            "user-bind-event",
            "user-unbind-event",
            "svt-unbind-event",
            "offboard-unbind-event");

    public static final List<String> INCOMING_KAFKA_EVENT_DETAIL_TYPES = List.of(
            "user-bound-event",
            "user-unbound-event",
            "svt-bound-event",
            "svt-unbound-event",
            "offboard-unbound-event");

    public static final String ROOM_SERVICE_VP_NOTIFICATION_KAFKA_TOPIC = "LIVE.telematics.room-notifications.2";
    public static final String CLC_EVENTBRIDGE_PIPES = "clc-eventbridge-pipes";
    public static String CLOUDWATCH_LOGGROUP_PREFIX = "/aws/vendedlogs/";

    public static final String HANDLER_NAME = "clc-owner-vehicle-events-room-notification-lambda";
    public static final String HANDLER_ROLE = "clc-owner-vehicle-events-room-notification-lambda-role";
    public static final String EVENT_LAMBDA_DLQ_NAME = "clc-owner-vehicle-events-room-notification-lambda-dlq";
}
