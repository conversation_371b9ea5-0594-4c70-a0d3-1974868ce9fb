/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jaguarlandrover.clc;

import com.jaguarlandrover.clc.config.Config;
import com.jaguarlandrover.clc.config.ConfigLoader;
import com.jaguarlandrover.clc.stacks.EventBridgeTagsStack;
import com.jaguarlandrover.clc.stacks.EventBusStack;
import com.jaguarlandrover.clc.stacks.EventKinesisFirehoseRuleStack;
import com.jaguarlandrover.clc.stacks.EventRuleStack;
import com.jaguarlandrover.clc.stacks.EventSchemaDiscoveryStack;
import com.jaguarlandrover.clc.util.ResourceTags;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import software.amazon.awscdk.App;
import software.amazon.awscdk.Environment;
import software.amazon.awscdk.StackProps;
import software.amazon.awscdk.Tags;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class BindingEventBusApp {

    private static final Logger LOGGER = LogManager.getLogger(BindingEventBusApp.class);

    public static void main(final String[] args) {

        App app = new App();

        final String ciEnv = System.getenv().getOrDefault("CI_ENVIRONMENT_NAME",
                "ecosystem-integration-developers");
        LOGGER.info("CI_ENVIRONMENT_NAME: {}", ciEnv);

        Config config = ConfigLoader.loadConfiguration(ciEnv);

        Environment env = Environment.builder()
                .account(config.getAccount())
                .region(config.getRegion())
                .build();

        EventBusStack eventBusStack = new EventBusStack(app, "ClcEventBusStack",
                StackProps.builder()
                        .env(env)
                        .build(),
                config);

        EventRuleStack eventRuleStack = new EventRuleStack(app, "ClcEventRuleStack",
                StackProps.builder().build(),
                eventBusStack.bus, config);
        EventSchemaDiscoveryStack eventSchemaDiscoveryStack = new EventSchemaDiscoveryStack(app,
                "ClcEventSchemaDiscoveryStack", StackProps.builder().build(), eventBusStack.bus);
        EventKinesisFirehoseRuleStack eventKinesisFirehoseRuleStack = new EventKinesisFirehoseRuleStack(app,
                "ClcKinesisEventRuleStack", StackProps.builder().build(), eventBusStack.bus, config);

        // Add dependency
        eventRuleStack.addDependency(eventBusStack);
        eventSchemaDiscoveryStack.addDependency(eventBusStack);
        eventKinesisFirehoseRuleStack.addDependency(eventBusStack);

        List<String> ruleArnList = Stream.concat(eventKinesisFirehoseRuleStack.ruleArnList.stream(),
                eventRuleStack.ruleArnList.stream()).collect(Collectors.toList());

        EventBridgeTagsStack eventbridgeTagsStack = new EventBridgeTagsStack(app, "ClcEventBridgeTagsStack",
                StackProps.builder().build(), ruleArnList, config);

        // Add dependency
        eventbridgeTagsStack.addDependency(eventRuleStack);
        eventbridgeTagsStack.addDependency(eventKinesisFirehoseRuleStack);

        for (Map.Entry<String, String> tag : ResourceTags.getTags(config).entrySet()) {
            Tags.of(app).add(tag.getKey(), tag.getValue());
        }

        app.synth();
    }
}