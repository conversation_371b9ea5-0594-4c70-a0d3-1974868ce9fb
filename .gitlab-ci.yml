variables:
  DEV_ENVIRONMENT: ecosystem-integration-developers
  PRE_PROD_ENVIRONMENT: ecosystem-integration-pre-production
  PROD_ENVIRONMENT: ecosystem-integration-production
  CN_DEV_ENVIRONMENT: "ecosystem-integration-cn-dev"
  CN_PREPROD_ENVIRONMENT: "ecosystem-integration-cn-preprod"
  CN_PROD_ENVIRONMENT: "ecosystem-integration-cn-prod"
  CN_DEV_PIPELINE_ROLE: "arn:aws-cn:iam::075661729253:role/cdk-pipeline-role"
  CN_PREPROD_PIPELINE_ROLE: "arn:aws-cn:iam::076032632511:role/cdk-pipeline-role"
  # CN_PROD_PIPELINE_ROLE: ""

include:
  - project: "d9/infrastructure/gitlab-ci-templates"
    ref: master
    file: "/.lambda-java-cdk-gitlab-ci-template.yml"
  - project: "d9/infrastructure/gitlab-ci-templates"
    ref: master
    file: "/.lambda-java-cdk-gitlab-ci-template-cn.patch.yml"
    inputs:
      deploy_aws_dev_cn: true
      deploy_aws_preprod_cn: true
      deploy_aws_prod_cn: false

build_lambdas:
  tags:
     - d9-runners
  script:
    - bash -l -c "cd software/ && mvn $MAVEN_CLI_OPTS package -Pnative -D skipTests"

cdk_diff:
  extends: .install_newest_aws_cdk
  stage: validate
  image: $CUSTOM_CDK_IMAGE
  tags:
    - aws
    - eco-int-dev
  needs:
    - build_lambdas
    - cdk_synth
  script:
    - cd infrastructure
    - cdk diff
  rules:
    - !reference [ .sonar_on_mr_rules, rules ]

cdk_synth:
  tags:
    - aws
    - eco-int-dev

cdk_synth_cn:
  tags:
    - ecosystem-integration-cn-dev

cdk_doctor:
  tags:
    - aws
    - eco-int-dev

.deploy:
  tags:
    - aws
    - eco-int-dev

deploy_aws_dev:
  tags:
    - aws
    - eco-int-dev
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH
      when: on_success
    - if: $CI_COMMIT_BRANCH
      when: manual


deploy_aws_dev_cn:
  tags:
    - ecosystem-integration-cn-dev

deploy_aws_preprod:
  tags:
    - aws
    - eco-int-pre-prod
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH
      when: on_success
    - if: $CI_COMMIT_BRANCH
      when: manual

deploy_aws_preprod_cn:
  tags:
    - ecosystem-integration-cn-preprod

deploy_aws_prod:
  tags:
    - aws
    - eco-int-prod

deploy_aws_prod_cn:
  tags:
    - ecosystem-integration-cn-prod
