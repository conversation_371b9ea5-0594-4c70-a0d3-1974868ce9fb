# clc-binding-eventbus

This is a CDK project for EventBridge functions with capabilities such as [archiving of events](https://confluence.devops.jlr-apps.com/display/VCDP/Archiving+of+Events+for+review), [failure handling](https://confluence.devops.jlr-apps.com/display/VCDP/Failure+Handling) - [logging, monitoring and alerting](https://confluence.devops.jlr-apps.com/display/VCDP/Logging%2C+Monitoring+and+Alerting+of+Events), [schemas discovery](https://confluence.devops.jlr-apps.com/display/VCDP/Event+Schemas) and [gitlab pipelines](https://confluence.devops.jlr-apps.com/display/VCDP/GitLab+Pipelines).

As part of the Connectivity platform, this application allows cross account communication to other JLR subsystems by receiving and passing the received events.

It is a [Maven](https://maven.apache.org/) based project, so you can open this project with any Maven compatible Java IDE to build and run tests.

## Current Architecture Resources

- AWS CDK
- Amazon EventBridge (EventBus and EventBridge Pipes)
- AWS Kinesis Data Firehose
- AWS Schema Discovery
- AWS S3
- AWS Lambda
- AWS Secrets Manager
- Quarkus native
- GraalVM
- Gitlab Pipelines
- Spock test framework
- Datadog integration

## Components of the Application

### Infrastructure

Infrastructure containing constructs grouped inside stacks that are deployed as single units using CDK.

- infrastructure/src/main/java/com/jaguarlandrover/clc/constructs/* - AWS CloudFormation resources and their configurations using level 1 and 2 constructs
- infrastructure/src/main/java/com/jaguarlandrover/clc/config/* - Loads and provides environment properties for the stacks
- infrastructure/src/main/java/com/jaguarlandrover/clc/stacks/* - Stacks grouping constructs deployed as a single unit.
- infrastructure/src/main/java/com/jaguarlandrover/clc/util/ResourceTags - AWS [tags](https://confluence.devops.jlr-apps.com/display/VCDP/AWS+Tagging+Policies+Guide) as custom attribute labels for resources to facilitate identification based on ownership, service, environment, and various other criteria.
- infrastructure/src/main/java/com/jaguarlandrover/clc/BindingEventBusApp.java - App Container for stacks' scope and instantiation in the application.

BindingEventBusApp instantiates the stacks and allows them to be synthesized into an AWS CloudFormation template and deployed.

The `cdk.json` file tells the CDK Toolkit how to execute your app.

This project contains a .gitlab-ci.yml for the deployment of the resources through CI/CD pipelines. The script runs the steps for cdk synth and deploy.

### Software

- software/src/main/java/com/jaguarlandrover/clc/* - software containing lambda components: lambda, service, model, exception, EventBus config and constant classes.

The software component of this application is built with Quarkus cloud-native framework and the build and deploy instructions are described in the next sections.
A lambda is necessary for the decoding and conversion of the Room notification details in Base64 format into a record that can be consumed by other services in VCDP.

## Incoming and Outgoing Events

This project implements EventBus and EventBridge pipes to receive and send events across subsystems. The Incoming and Outgoing events are as below:

### Incoming events

The incoming events can be an event from another EventBus or a Kafka notification from a topic managed in AWS MSK - Managed Streaming for Apache Kafka (owned by Room service).
The notifications from a Kafka topic are received through EventBridge Pipes integration with AWS MSK. 
The notification details are encoded in Base64 format.

| Event Type             | Source        | Target                            | Sample                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
|------------------------|---------------|-----------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| user-bind-event        | VCDP EventBus | clc-event-bridge-vroom-eco Lambda | { source: vcdp-event-enrichment-service, detailType: user-bind-event, detail: { "metadata": { "correlationId": "1b2e8-f31d-4af3-a0db-47ec6c991df8", "schemaVersion": 1 }, "data": { "vin": "TSTLRGBVDNRIG7117", "userId": "urn:iam2-mgd-v1:mgd-identifiers:customer:auto-id:principal-uuid:286b4655-5665-4a3e-b207-15d37d4b8f05", "exclusiveFrom": "2024-05-03T14:16:15.593380+00:00", "bindingType": "OWNER_KEY_BIND or FRIEND_KEY_BIND", "userIdType": "FORGEROCK" } } }                                                         |
| user-unbind-event      | VCDP EventBus | clc-event-bridge-vroom-eco Lambda | { source: vcdp-event-enrichment-service, detailType: user-unbind-event, detail: { "metadata": { "correlationId": "1b2e8-f31d-4af3-a0db-47ec6c991df8", "schemaVersion": 1 }, "data": { "vin": "TSTLRGBVDNRIG7117", "userId": "urn:iam2-mgd-v1:mgd-identifiers:customer:auto-id:principal-uuid:286b4655-5665-4a3e-b207-15d37d4b8f05", "exclusiveTo": "2024-05-03T14:16:15.593380+00:00", "unbindingType": "OWNER_KEY_UNBIND or FRIEND_KEY_UNBIND", "userIdType": "FORGEROCK" } } }                                                   | 
| offboard-unbind-event  | VCDP EventBus | clc-event-bridge-vroom-eco Lambda | { source: vcdp-event-enrichment-service, detailType: offboard-unbind-event, detail: { "metadata": { "correlationId": "1b2e8-f31d-4af3-a0db-47ec6c991df8", "schemaVersion": 1 }, "data": { "vin": "TSTLRGBVDNRIG7117", "userId": "urn:iam2-mgd-v1:mgd-identifiers:customer:auto-id:principal-uuid:286b4655-5665-4a3e-b207-15d37d4b8f05", "exclusiveTo": "2024-05-03T14:16:15.593380+00:00", "unbindingType": "OWNER_KEY_UNBIND", "userIdType": "FORGEROCK" } } }                                                                    |
| svt-unbind-event       | VCDP EventBus | clc-event-bridge-vroom-eco Lambda | { source: vcdp-event-enrichment-service, detailType: svt-unbind-event, detail: { "metadata": { "correlationId": "1b2e8-f31d-4af3-a0db-47ec6c991df8", "schemaVersion": 1 }, "data": { "vin": "TSTLRGBVDNRIG7117", "userId": "urn:iam2-mgd-v1:mgd-identifiers:customer:auto-id:principal-uuid:286b4655-5665-4a3e-b207-15d37d4b8f05", "exclusiveTo": "2024-05-03T14:16:15.593380+00:00", "unbindingType": "OWNER_KEY_UNBIND", "userIdType": "FORGEROCK" } } }                                                                         |
| user-bound-event       | Room Kafka    | Ecosystem EventBus                | { eventSource: aws :kafka, topic: LIVE.telematics.room-notifications.2, value: <Encoded Base64 string representing this data: { "vin": "TSTLRGBVDNRIG7117", "vpaId": "664f47cf21a0286015618a4e", "userId": "urn:iam2-mgd-v1:mgd-identifiers:customer:auto-id:principal-uuid:286b4655-5665-4a3e-b207-15d37d4b8f05", "userIdType": "FORGEROCK or SAP_CDC", "eventType": "ASSOCIATION_CREATED", "associationType": "OWNER_KEY" or "FRIEND_KEY", "eventTimestamp": "2007-03-01T13:00:00Z/2008-05-11T15:30:00Z", provisioned: true }> } |
| user-unbound-event     | Room Kafka    | Ecosystem EventBus                | { eventSource: aws :kafka, topic: LIVE.telematics.room-notifications.2, value: <Encoded Base64 string representing this data: { "vin": "TSTLRGBVDNRIG7117", "vpaId": "664f47cf21a0286015618a4e", "userId": "urn:iam2-mgd-v1:mgd-identifiers:customer:auto-id:principal-uuid:286b4655-5665-4a3e-b207-15d37d4b8f05", "userIdType": "FORGEROCK or SAP_CDC", "eventType": "ASSOCIATION_EXPIRED", "associationType": "OWNER_KEY" or "FRIEND_KEY", "eventTimestamp": "2007-03-01T13:00:00Z/2008-05-11T15:30:00Z", provisioned: true }> } |
| offboard-unbound-event | Room Kafka    | Ecosystem EventBus                | { eventSource: aws :kafka, topic: LIVE.telematics.room-notifications.2, value: <Encoded Base64 string representing this data: { "vin": "TSTLRGBVDNRIG7117", "vpaId": "664f47cf21a0286015618a4e", "userId": "urn:iam2-mgd-v1:mgd-identifiers:customer:auto-id:principal-uuid:286b4655-5665-4a3e-b207-15d37d4b8f05", "userIdType": "FORGEROCK or SAP_CDC", "eventType": "ASSOCIATION_EXPIRED", "associationType": "OFFBOARD", "eventTimestamp": "2007-03-01T13:00:00Z/2008-05-11T15:30:00Z", provisioned: true }> }                  |
| svt-bound-event        | Room Kafka    | Ecosystem EventBus                | { eventSource: aws :kafka, topic: LIVE.telematics.room-notifications.2, value: <Encoded Base64 string representing this data: { "vin": "TSTLRGBVDNRIG7117", "vpaId": "664f47cf21a0286015618a4e", "userId": "urn:iam2-mgd-v1:mgd-identifiers:customer:auto-id:principal-uuid:286b4655-5665-4a3e-b207-15d37d4b8f05", "userIdType": "FORGEROCK or SAP_CDC", "eventType": "ASSOCIATION_CREATED", "associationType": "SVT", "eventTimestamp": "2007-03-01T13:00:00Z/2008-05-11T15:30:00Z", provisioned: true }> } }                     |
| svt-unbound-event      | Room Kafka    | Ecosystem EventBus                | { eventSource: aws :kafka, topic: LIVE.telematics.room-notifications.2, value: <Encoded Base64 string representing this data: { "vin": "TSTLRGBVDNRIG7117", "vpaId": "664f47cf21a0286015618a4e", "userId": "urn:iam2-mgd-v1:mgd-identifiers:customer:auto-id:principal-uuid:286b4655-5665-4a3e-b207-15d37d4b8f05", "userIdType": "FORGEROCK or SAP_CDC", "eventType": "ASSOCIATION_EXPIRED", "associationType": "SVT", "eventTimestamp": "2007-03-01T13:00:00Z/2008-05-11T15:30:00Z", provisioned: true }> } }                     |


### Outgoing events

| Event Type             | Source              | Target         | Sample                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
|------------------------|---------------------|----------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| user-bound-event       | Ecosystem EventBus  | VCDP EventBus  | { source: ecosystem-event-service, detailType: user-bound-event, detail: { "metadata": { "correlationId": "1b2e8-f31d-4af3-a0db-47ec6c991df8", "schemaVersion": 1 }, "data": { "vin": "TSTLRGBVDNRIG7117", "vpaId": "664f47cf21a0286015618a4e", "userId": "urn:iam2-mgd-v1:mgd-identifiers:customer:auto-id:principal-uuid:286b4655-5665-4a3e-b207-15d37d4b8f05", "userType": "PAAKOWNER or PAAKFRIEND", "userIdType": "FORGEROCK or SAP_CDC"  } } }    |
| user-unbound-event     | Ecosystem EventBus  | VCDP EventBus  | { source: ecosystem-event-service, detailType: user-unbound-event, detail: { "metadata": { "correlationId": "1b2e8-f31d-4af3-a0db-47ec6c991df8", "schemaVersion": 1 }, "data": { "vin": "TSTLRGBVDNRIG7117", "vpaId": "664f47cf21a0286015618a4e", "userId": "urn:iam2-mgd-v1:mgd-identifiers:customer:auto-id:principal-uuid:286b4655-5665-4a3e-b207-15d37d4b8f05", "userType": "PAAKOWNER or PAAKFRIEND", "userIdType": "FORGEROCK or SAP_CDC"  } } }  |
| offboard-unbound-event | Ecosystem EventBus  | VCDP EventBus  | { source: ecosystem-event-service, detailType: offboard-unbound-event, detail: { "metadata": { "correlationId": "1b2e8-f31d-4af3-a0db-47ec6c991df8", "schemaVersion": 1 }, "data": { "vin": "TSTLRGBVDNRIG7117", "vpaId": "664f47cf21a0286015618a4e", "userId": "urn:iam2-mgd-v1:mgd-identifiers:customer:auto-id:principal-uuid:286b4655-5665-4a3e-b207-15d37d4b8f05", "userType": "PAAKOWNER", "userIdType": "FORGEROCK or SAP_CDC" } } }             |
| svt-bound-event        | Ecosystem EventBus  | VCDP EventBus  | { source: ecosystem-event-service, detailType: svt-bound-event, detail: { "metadata": { "correlationId": "1b2e8-f31d-4af3-a0db-47ec6c991df8", "schemaVersion": 1 }, "data": { "vin": "TSTLRGBVDNRIG7117", "vpaId": "664f47cf21a0286015618a4e", "userId": "urn:iam2-mgd-v1:mgd-identifiers:customer:auto-id:principal-uuid:286b4655-5665-4a3e-b207-15d37d4b8f05", "userType": "PAAKOWNER", "userIdType": "FORGEROCK or SAP_CDC" } } }                    |
| svt-unbound-event      | Ecosystem EventBus  | VCDP EventBus  | { source: ecosystem-event-service, detailType: svt-unbound-event, detail: { "metadata": { "correlationId": "1b2e8-f31d-4af3-a0db-47ec6c991df8", "schemaVersion": 1 }, "data": { "vin": "TSTLRGBVDNRIG7117", "vpaId": "664f47cf21a0286015618a4e", "userId": "urn:iam2-mgd-v1:mgd-identifiers:customer:auto-id:principal-uuid:286b4655-5665-4a3e-b207-15d37d4b8f05", "userType": "PAAKOWNER", "userIdType": "FORGEROCK or SAP_CDC" } } }                  |


### Send events directly through AWS Console

You can use Amazon EventBridge console to quickly Dev test the flows for bind/unbind and bound/unbound vehicle-person. 

1. Log into the event source account (VCDP or Ecosystem, check the above tables), go to Amazon EventBridge.
2. Select the EventBus (clc-event-bus) and click Send events. 
3. Fill the source and detailType fields accordingly to the examples above.
4. For the detail, use the detail objet from the above table. 
5. Click in Send.

If the target is another EventBus, check if the event hit its target by checking the event in the S3 event audit bucket in the AWS account of the event target.
If the target is an API or lambda, check the logs in Cloudwatch.

### Send Kafka topic notifications

To test the connection between AWS MSK (Kafka) and EventBridge Pipes:

1. log in to Ecosystem account and go to an [EC2 instance](https://eu-west-1.console.aws.amazon.com/ec2/home?region=eu-west-1#InstanceDetails:instanceId=i-0e909c13049b34590). You will use this client machine to produce data into the topic. This client machine has already Java, Kafka and Amazon MSK IAM installed. Amazon MSK IAM makes it possible for the client machine to access the cluster that lives in a private VPC.
2. Click Connect, and click Connect again in the next page.
3. It will prompt a terminal. Run the following:

```bash
cd kafka

kafka_2.13-3.5.1/bin/kafka-console-producer.sh --broker-list b-2.mskecodev.lzrvk9.c1.kafka.eu-west-1.amazonaws.com:9098 --producer.config kafka_2.13-3.5.1/bin/client.properties.txt --topic LIVE.telematics.room-notifications.2
```

4. The last command will prompt you to insert your Kafka notification. Copy one of the Kafka notifications in the above table and paste into the terminal.

It will record a notification into the topic that will be listened by EventBridge Pipes.
If successful, you will be able to see the successful execution in EventBridge Pipes | Monitoring | Logs.

## Deployment through pipeline

Currently, this project uses java-lambda pipeline template for deploying resources.

## Build and deploy the application from your local machine

To deploy the app, you need the following tools.

* AWS CLI - [Install the AWS CLI](https://docs.aws.amazon.com/cli/latest/userguide/cli-chap-install.html) and [configure it with your AWS credentials].
* java17 - [Install the Java 17](https://docs.aws.amazon.com/corretto/latest/corretto-17-ug/downloads-list.html)
* Maven - [Install Maven](https://maven.apache.org/install.html)
* Docker - [Install Docker community edition](https://hub.docker.com/search/?type=edition&offering=community)

### Quarkus native build

This project uses Quarkus native build. Use the below commands to compile and build the application.
The prerequisites to building a native executable are using a distribution of GraalVM and installing Quarkus cli (if you want to use it to build). Check this [guide](https://quarkus.io/guides/building-native-image) to set up your machine to build a native executable.

To compile natively and run tests, start your Docker and run:

```bash
cd software
mvn clean install -Pnative '-Dquarkus.native.container-build=true'
```
- It will build an executable and compile all dependencies into a function.zip file. This step is necessary if you want to deploy this application with CDK (<font color="red"><ins>**Only for test**</ins></font> stack) with a native executable from your local.

- Note: Although the above commands work for building a native executable, if you are not using Linux, the executable is not compatible with the CLC Lambdas runtime in AWS. Refer to the previous link for more details on how to build natively for Linux.


### Deployment from local machine

This step is documented <font color="red"><ins>**Just for test purpose**</ins></font>. Strictly avoid deployment through terminal if deploying to the main stacks.

1. Add the following snippet to `~/.aws/config`. You can manually create this file if it does not exist.

```bash
[default]
region=eu-west-1
output=json
aws_access_key_id=<YOUR ACCESS KEY>
aws_secret_access_key=<YOUR SECRET>
aws_session_token=<YOUR SESSION TOKEN>

[profile ecosystem-developers]
sso_session = ecosystem-developers-sso-session
sso_account_id = ************
sso_role_name = Accenture-access-dev
region = eu-west-1
output = json

[sso-session ecosystem-developers-sso-session]
sso_start_url = https://jlr-voba.awsapps.com/start#/
sso_region = eu-west-1
sso_registration_scopes = sso:account:access

```

2. Update your profile in `~/.aws/credentials` with a new session token and credentials.

```bash
[default]
aws_access_key_id=<your-aws_access_key_id>
aws_secret_access_key=<your-aws_secret_access_key>
aws_session_token=<your-aws_session_token>
````

3. Login to the environment (vcdp-developers) using the below command. It might give you options for which environment you want to deploy into, choose account ************.

```bash
aws sso login --profile ecosystem-developers
```

4. To synthetize a CloudFormation template and deploy all the stacks run the below commands under infrastructure directory:

```bash
cdk bootstrap --profile ecosystem-developers
cdk deploy --profile ecosystem-developers
```

## Unit Tests

Unit tests are implemented with Spock testing and specification framework following JLR [guidelines](https://confluence.devops.jlr-apps.com/display/VCDP/Spike%3A+Research+Groovy+for+Unit+Testing+Purposes) and other JLR projects.
Run tests and debug with your IDE or alternatively, run with Maven.


## CheckStyle

1. Vehicle Lifecycle uses CheckStyle with Maven and IDE plugins. Previously to pushing your code to remote, you would want to configure your IDE to scan your code against CheckStyle issues.

2. Run `mvn clean verify` to verify linting issues before pushing it into remote.


## License and copyright

Copyright (c) 2025. Jaguar Land Rover - All Rights Reserved.

CONFIDENTIAL INFORMATION - DO NOT DISTRIBUTE